kernel-netlink {

    # Buffer size for received Netlink messages.
    # buflen = <min(PAGE_SIZE, 8192)>

    # Force maximum Netlink receive buffer on Netlink socket.
    # force_receive_buffer_size = no

    # Firewall mark to set on the routing rule that directs traffic to our
    # routing table.
    fwmark = !0x42

    # Interface to be used to find hardware offload feature flag on.
    # hw_offload_feature_interface = lo

    # Whether to ignore errors potentially resulting from a retransmission.
    # ignore_retransmit_errors = no

    # Whether to load the plugin. Can also be an integer to increase the
    # priority of this plugin.
    load = yes

    # MSS to set on installed routes, 0 to disable.
    # mss = 0

    # MTU to set on installed routes, 0 to disable.
    # mtu = 0

    # Whether to perform concurrent Netlink ROUTE queries on a single socket.
    # parallel_route = no

    # Whether to perform concurrent Netlink XFRM queries on a single socket.
    # parallel_xfrm = no

    # Whether to always use XFRM_MSG_UPDPOLICY to install policies.
    # policy_update = no

    # Whether to use port or socket based IKE XFRM bypass policies.
    # port_bypass = no

    # Whether to process changes in routing rules to trigger roam events.
    # process_rules = no

    # Maximum Netlink socket receive buffer in bytes.
    # receive_buffer_size = 0

    # Number of Netlink message retransmissions to send on timeout.
    # retries = 0

    # Whether to trigger roam events when interfaces, addresses or routes
    # change.
    # roam_events = yes

    # Whether to set protocol and ports in the selector installed on transport
    # mode IPsec SAs in the kernel.
    # set_proto_port_transport_sa = no

    # Netlink message retransmission timeout, 0 to disable retransmissions.
    # timeout = 0

    # Lifetime of XFRM acquire state and allocated SPIs in kernel.
    # xfrm_acq_expires = 165

    # XFRM policy hashing threshold configuration for IPv4 and IPv6.
    spdh_thresh {

        ipv4 {

            # Local subnet XFRM policy hashing threshold for IPv4.
            # lbits = 32

            # Remote subnet XFRM policy hashing threshold for IPv4.
            # rbits = 32

        }

        ipv6 {

            # Local subnet XFRM policy hashing threshold for IPv6.
            # lbits = 128

            # Remote subnet XFRM policy hashing threshold for IPv6.
            # rbits = 128

        }

    }

}

