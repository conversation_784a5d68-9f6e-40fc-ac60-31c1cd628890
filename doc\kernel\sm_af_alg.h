/* SPDX-License-Identifier: GPL-2.0 */
/*
 * sm_af_alg.h - 国密算法AF_ALG接口头文件
 * 定义用户空间和内核空间交互的数据结构和常量
 * 为SM2/SM3/SM4算法提供统一的AF_ALG接口定义
 */

#ifndef _SM_AF_ALG_H
#define _SM_AF_ALG_H

#include <linux/types.h>
#include <linux/socket.h>
#include <crypto/if_alg.h>
#include <crypto/sm2.h>  // 包含SM2结构体定义

/* AF_ALG操作类型定义 */
#define ALG_OP_SM2_KEYGEN    1   /* SM2密钥生成 */
#define ALG_OP_SM2_SIGN      2   /* SM2签名 */
#define ALG_OP_SM2_VERIFY    3   /* SM2验证 */
#define ALG_OP_SM2_ENCRYPT   4   /* SM2加密 */
#define ALG_OP_SM2_DECRYPT   5   /* SM2解密 */
#define ALG_OP_SM3_HASH      6   /* SM3哈希 */
#define ALG_OP_SM3_HMAC      7   /* SM3 HMAC */
#define ALG_OP_SM3_KDF       8   /* SM3密钥派生 */
#define ALG_OP_SM4_ENCRYPT   9   /* SM4加密 */
#define ALG_OP_SM4_DECRYPT   10  /* SM4解密 */

/* AF_ALG算法族扩展定义 */
#define ALG_SET_SM2_USERID   1   /* 设置SM2用户ID */
#define ALG_SET_SM4_MODE     2   /* 设置SM4工作模式 */

/* SM4工作模式定义 */
#define SM4_MODE_ECB         1   /* ECB模式 */
#define SM4_MODE_CBC         2   /* CBC模式 */
#define SM4_MODE_CFB         3   /* CFB模式 */
#define SM4_MODE_OFB         4   /* OFB模式 */
#define SM4_MODE_CTR         5   /* CTR模式 */

///* SM2密钥对结构 */
//struct sm2_key_pair {
//    MPI private_key;
//    MPI_POINT public_key;
//};
//
///* SM2签名结构 */
//struct sm2_signature {
//    MPI r;
//    MPI s;
//};

/* strongSwan兼容的请求响应数据结构 */
struct sm_alg_req {
    __u32 operation;        /* 操作类型 */
    __u32 key_len;         /* 密钥长度 */
    __u32 data_len;        /* 数据长度 */
    __u32 output_len;      /* 预期输出长度 */
    __u32 user_id_len;     /* 用户ID长度（SM2专用） */
    __u32 mode;            /* 工作模式（SM4专用） */
    __u32 reserved[2];     /* 保留字段 */
    __u8 data[];           /* 变长数据：key + data + user_id + iv */
} __attribute__((packed));

struct sm_alg_resp {
    __u32 result;          /* 操作结果码 */
    __u32 output_len;      /* 实际输出长度 */
    __u32 reserved[2];     /* 保留字段 */
    __u8 data[];           /* 输出数据 */
} __attribute__((packed));

/* SM2算法专用结构 */
struct sm2_keypair_data {
    __u8 private_key[32];  /* 私钥，32字节 */
    __u8 public_key[65];   /* 公钥，65字节（04 + x + y） */
} __attribute__((packed));

struct sm2_signature_data {
    __u8 r[32];            /* 签名r分量，32字节 */
    __u8 s[32];            /* 签名s分量，32字节 */
} __attribute__((packed));

/* SM3算法专用结构 */
struct sm3_hash_data {
    __u8 digest[32];       /* SM3摘要值，32字节 */
} __attribute__((packed));

/* SM4算法专用结构 */
struct sm4_cipher_data {
    __u8 iv[16];           /* 初始向量，16字节 */
    __u8 data[];           /* 加密/解密数据 */
} __attribute__((packed));

/* 错误码定义 */
#define SM_ALG_SUCCESS       0    /* 操作成功 */
#define SM_ALG_ERROR_PARAM   -1   /* 参数错误 */
#define SM_ALG_ERROR_NOMEM   -2   /* 内存不足 */
#define SM_ALG_ERROR_NOKEY   -3   /* 密钥未设置 */
#define SM_ALG_ERROR_VERIFY  -4   /* 验证失败 */
#define SM_ALG_ERROR_CRYPTO  -5   /* 密码运算错误 */
#define SM_ALG_ERROR_NOTSUP  -6   /* 操作不支持 */

/* 内核模块接口声明 */
#ifdef __KERNEL__

/* SM2 AF_ALG上下文结构 */
struct sm2_alg_ctx {
    struct crypto_akcipher *tfm;
    struct mpi_ec_ctx ec_ctx;
    struct sm2_key_pair keypair;
    __u8 user_id[256];
    __u32 user_id_len;
    bool key_set;
    bool ctx_initialized;
};

/* SM3 AF_ALG上下文结构 */
struct sm3_alg_ctx {
    struct crypto_shash *tfm;
    struct sm3_context ctx;
    bool initialized;
    bool hmac_mode;
    __u8 hmac_key[256];
    __u32 hmac_key_len;
};

/* SM4 AF_ALG上下文结构 */
struct sm4_alg_ctx {
    struct crypto_skcipher *tfm;
    struct crypto_sm4_ctx sm4_ctx;
    __u8 key[16];
    __u8 iv[16];
    __u32 mode;
    bool key_set;
    bool iv_set;
};

/* 算法类型注册结构 */
const struct af_alg_type sm2algtype;
const struct af_alg_type sm3algtype;
const struct af_alg_type sm4algtype;

/* 内部函数声明 */
int sm2_alg_setkey(struct sock *sk, char __user *key, unsigned int keylen);
int sm2_alg_setsockopt(struct sock *sk, int level, int optname,
                       char __user *optval, unsigned int optlen);
int sm2_alg_sendmsg(struct socket *sock, struct msghdr *msg, size_t size);
int sm2_alg_recvmsg(struct socket *sock, struct msghdr *msg, size_t ignored, int flags);

int sm3_alg_init(struct sock *sk);
void sm3_alg_release(struct sock *sk);
int sm3_alg_setkey(struct sock *sk, char __user *key, unsigned int keylen);
int sm3_alg_sendmsg(struct socket *sock, struct msghdr *msg, size_t size);
int sm3_alg_recvmsg(struct socket *sock, struct msghdr *msg, size_t ignored, int flags);

int sm4_alg_setkey(struct sock *sk, char __user *key, unsigned int keylen);
int sm4_alg_setsockopt(struct sock *sk, int level, int optname,
                       char __user *optval, unsigned int optlen);
int sm4_alg_sendmsg(struct socket *sock, struct msghdr *msg, size_t size);
int sm4_alg_recvmsg(struct socket *sock, struct msghdr *msg, size_t ignored, int flags);

/* 模块初始化和清理函数 */
int __init sm_af_alg_init(void);
void __exit sm_af_alg_exit(void);

#endif /* __KERNEL__ */

#endif /* _SM_AF_ALG_H */