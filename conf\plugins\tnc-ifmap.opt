charon.plugins.tnc-ifmap.client_cert =
	Path to X.509 certificate file of IF-MAP client.

charon.plugins.tnc-ifmap.client_key =
	Path to private key file of IF-MAP client.

charon.plugins.tnc-ifmap.device_name =
	Unique name of strongSwan server as a PEP and/or PDP device.

charon.plugins.tnc-ifmap.renew_session_interval = 150
	Interval in seconds between periodic IF-MAP RenewSession requests.

charon.plugins.tnc-ifmap.server_uri = https://localhost:8444/imap
	URI of the form [https://]servername[:port][/path].

charon.plugins.tnc-ifmap.server_cert =
	Path to X.509 certificate file of IF-MAP server.

charon.plugins.tnc-ifmap.username_password =
	Credentials of IF-MAP client of the form username:password. If set, make
	sure to adjust the permissions of the config file accordingly.
