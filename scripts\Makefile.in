# Makefile.in generated by automake 1.16.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
noinst_PROGRAMS = bin2array$(EXEEXT) bin2sql$(EXEEXT) id2sql$(EXEEXT) \
	key2keyid$(EXEEXT) keyid2sql$(EXEEXT) oid2der$(EXEEXT) \
	thread_analysis$(EXEEXT) dh_speed$(EXEEXT) \
	pubkey_speed$(EXEEXT) crypt_burn$(EXEEXT) hash_burn$(EXEEXT) \
	fetch$(EXEEXT) dnssec$(EXEEXT) malloc_speed$(EXEEXT) \
	aes-test$(EXEEXT) settings-test$(EXEEXT) timeattack$(EXEEXT) \
	$(am__EXEEXT_1)
@USE_TLS_TRUE@am__append_1 = tls_test
subdir = scripts
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/config/libtool.m4 \
	$(top_srcdir)/m4/config/ltoptions.m4 \
	$(top_srcdir)/m4/config/ltsugar.m4 \
	$(top_srcdir)/m4/config/ltversion.m4 \
	$(top_srcdir)/m4/config/lt~obsolete.m4 \
	$(top_srcdir)/m4/macros/split-package-version.m4 \
	$(top_srcdir)/m4/macros/with.m4 \
	$(top_srcdir)/m4/macros/enable-disable.m4 \
	$(top_srcdir)/m4/macros/add-plugin.m4 \
	$(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
@USE_TLS_TRUE@am__EXEEXT_1 = tls_test$(EXEEXT)
PROGRAMS = $(noinst_PROGRAMS)
aes_test_SOURCES = aes-test.c
aes_test_OBJECTS = aes-test.$(OBJEXT)
aes_test_DEPENDENCIES =  \
	$(top_builddir)/src/libstrongswan/libstrongswan.la
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
am_bin2array_OBJECTS = bin2array.$(OBJEXT)
bin2array_OBJECTS = $(am_bin2array_OBJECTS)
bin2array_LDADD = $(LDADD)
am_bin2sql_OBJECTS = bin2sql.$(OBJEXT)
bin2sql_OBJECTS = $(am_bin2sql_OBJECTS)
bin2sql_LDADD = $(LDADD)
am_crypt_burn_OBJECTS = crypt_burn.$(OBJEXT)
crypt_burn_OBJECTS = $(am_crypt_burn_OBJECTS)
crypt_burn_DEPENDENCIES =  \
	$(top_builddir)/src/libstrongswan/libstrongswan.la
am_dh_speed_OBJECTS = dh_speed.$(OBJEXT)
dh_speed_OBJECTS = $(am_dh_speed_OBJECTS)
am__DEPENDENCIES_1 =
dh_speed_DEPENDENCIES =  \
	$(top_builddir)/src/libstrongswan/libstrongswan.la \
	$(am__DEPENDENCIES_1)
am_dnssec_OBJECTS = dnssec.$(OBJEXT)
dnssec_OBJECTS = $(am_dnssec_OBJECTS)
dnssec_DEPENDENCIES =  \
	$(top_builddir)/src/libstrongswan/libstrongswan.la
am_fetch_OBJECTS = fetch.$(OBJEXT)
fetch_OBJECTS = $(am_fetch_OBJECTS)
fetch_DEPENDENCIES =  \
	$(top_builddir)/src/libstrongswan/libstrongswan.la
am_hash_burn_OBJECTS = hash_burn.$(OBJEXT)
hash_burn_OBJECTS = $(am_hash_burn_OBJECTS)
hash_burn_DEPENDENCIES =  \
	$(top_builddir)/src/libstrongswan/libstrongswan.la
am_id2sql_OBJECTS = id2sql.$(OBJEXT)
id2sql_OBJECTS = $(am_id2sql_OBJECTS)
id2sql_DEPENDENCIES =  \
	$(top_builddir)/src/libstrongswan/libstrongswan.la
am_key2keyid_OBJECTS = key2keyid.$(OBJEXT)
key2keyid_OBJECTS = $(am_key2keyid_OBJECTS)
key2keyid_DEPENDENCIES =  \
	$(top_builddir)/src/libstrongswan/libstrongswan.la
am_keyid2sql_OBJECTS = keyid2sql.$(OBJEXT)
keyid2sql_OBJECTS = $(am_keyid2sql_OBJECTS)
keyid2sql_DEPENDENCIES =  \
	$(top_builddir)/src/libstrongswan/libstrongswan.la
am_malloc_speed_OBJECTS = malloc_speed.$(OBJEXT)
malloc_speed_OBJECTS = $(am_malloc_speed_OBJECTS)
malloc_speed_DEPENDENCIES =  \
	$(top_builddir)/src/libstrongswan/libstrongswan.la \
	$(am__DEPENDENCIES_1)
am_oid2der_OBJECTS = oid2der.$(OBJEXT)
oid2der_OBJECTS = $(am_oid2der_OBJECTS)
oid2der_DEPENDENCIES =  \
	$(top_builddir)/src/libstrongswan/libstrongswan.la
am_pubkey_speed_OBJECTS = pubkey_speed.$(OBJEXT)
pubkey_speed_OBJECTS = $(am_pubkey_speed_OBJECTS)
pubkey_speed_DEPENDENCIES =  \
	$(top_builddir)/src/libstrongswan/libstrongswan.la \
	$(am__DEPENDENCIES_1)
settings_test_SOURCES = settings-test.c
settings_test_OBJECTS = settings-test.$(OBJEXT)
settings_test_DEPENDENCIES =  \
	$(top_builddir)/src/libstrongswan/libstrongswan.la
am_thread_analysis_OBJECTS = thread_analysis.$(OBJEXT)
thread_analysis_OBJECTS = $(am_thread_analysis_OBJECTS)
thread_analysis_LDADD = $(LDADD)
am_timeattack_OBJECTS = timeattack.$(OBJEXT)
timeattack_OBJECTS = $(am_timeattack_OBJECTS)
timeattack_DEPENDENCIES =  \
	$(top_builddir)/src/libstrongswan/libstrongswan.la \
	$(am__DEPENDENCIES_1)
am__tls_test_SOURCES_DIST = tls_test.c
@USE_TLS_TRUE@am_tls_test_OBJECTS = tls_test.$(OBJEXT)
tls_test_OBJECTS = $(am_tls_test_OBJECTS)
@USE_TLS_TRUE@tls_test_DEPENDENCIES = $(top_builddir)/src/libstrongswan/libstrongswan.la \
@USE_TLS_TRUE@	$(top_builddir)/src/libtls/libtls.la
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@ -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/aes-test.Po ./$(DEPDIR)/bin2array.Po \
	./$(DEPDIR)/bin2sql.Po ./$(DEPDIR)/crypt_burn.Po \
	./$(DEPDIR)/dh_speed.Po ./$(DEPDIR)/dnssec.Po \
	./$(DEPDIR)/fetch.Po ./$(DEPDIR)/hash_burn.Po \
	./$(DEPDIR)/id2sql.Po ./$(DEPDIR)/key2keyid.Po \
	./$(DEPDIR)/keyid2sql.Po ./$(DEPDIR)/malloc_speed.Po \
	./$(DEPDIR)/oid2der.Po ./$(DEPDIR)/pubkey_speed.Po \
	./$(DEPDIR)/settings-test.Po ./$(DEPDIR)/thread_analysis.Po \
	./$(DEPDIR)/timeattack.Po ./$(DEPDIR)/tls_test.Po
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = aes-test.c $(bin2array_SOURCES) $(bin2sql_SOURCES) \
	$(crypt_burn_SOURCES) $(dh_speed_SOURCES) $(dnssec_SOURCES) \
	$(fetch_SOURCES) $(hash_burn_SOURCES) $(id2sql_SOURCES) \
	$(key2keyid_SOURCES) $(keyid2sql_SOURCES) \
	$(malloc_speed_SOURCES) $(oid2der_SOURCES) \
	$(pubkey_speed_SOURCES) settings-test.c \
	$(thread_analysis_SOURCES) $(timeattack_SOURCES) \
	$(tls_test_SOURCES)
DIST_SOURCES = aes-test.c $(bin2array_SOURCES) $(bin2sql_SOURCES) \
	$(crypt_burn_SOURCES) $(dh_speed_SOURCES) $(dnssec_SOURCES) \
	$(fetch_SOURCES) $(hash_burn_SOURCES) $(id2sql_SOURCES) \
	$(key2keyid_SOURCES) $(keyid2sql_SOURCES) \
	$(malloc_speed_SOURCES) $(oid2der_SOURCES) \
	$(pubkey_speed_SOURCES) settings-test.c \
	$(thread_analysis_SOURCES) $(timeattack_SOURCES) \
	$(am__tls_test_SOURCES_DIST)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
ALLOCA = @ALLOCA@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
ATOMICLIB = @ATOMICLIB@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
BFDLIB = @BFDLIB@
BTLIB = @BTLIB@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
COVERAGE_CFLAGS = @COVERAGE_CFLAGS@
COVERAGE_LDFLAGS = @COVERAGE_LDFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLIB = @DLLIB@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
EASY_INSTALL = @EASY_INSTALL@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
FUZZING_CFLAGS = @FUZZING_CFLAGS@
FUZZING_LDFLAGS = @FUZZING_LDFLAGS@
GEM = @GEM@
GENHTML = @GENHTML@
GIT_VERSION = @GIT_VERSION@
GPERF = @GPERF@
GPERF_LEN_TYPE = @GPERF_LEN_TYPE@
GPRBUILD = @GPRBUILD@
GREP = @GREP@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LCOV = @LCOV@
LD = @LD@
LDFLAGS = @LDFLAGS@
LEX = @LEX@
LEXLIB = @LEXLIB@
LEX_OUTPUT_ROOT = @LEX_OUTPUT_ROOT@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
MYSQLCFLAG = @MYSQLCFLAG@
MYSQLCONFIG = @MYSQLCONFIG@
MYSQLLIB = @MYSQLLIB@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OPENSSL_LIB = @OPENSSL_LIB@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PACKAGE_VERSION_BUILD = @PACKAGE_VERSION_BUILD@
PACKAGE_VERSION_MAJOR = @PACKAGE_VERSION_MAJOR@
PACKAGE_VERSION_MINOR = @PACKAGE_VERSION_MINOR@
PACKAGE_VERSION_REVIEW = @PACKAGE_VERSION_REVIEW@
PATH_SEPARATOR = @PATH_SEPARATOR@
PERL = @PERL@
PKG_CONFIG = @PKG_CONFIG@
PKG_CONFIG_LIBDIR = @PKG_CONFIG_LIBDIR@
PKG_CONFIG_PATH = @PKG_CONFIG_PATH@
PLUGIN_CFLAGS = @PLUGIN_CFLAGS@
PTHREADLIB = @PTHREADLIB@
PYTHON = @PYTHON@
PYTHONEGGINSTALLDIR = @PYTHONEGGINSTALLDIR@
PYTHON_EXEC_PREFIX = @PYTHON_EXEC_PREFIX@
PYTHON_PACKAGE_VERSION = @PYTHON_PACKAGE_VERSION@
PYTHON_PLATFORM = @PYTHON_PLATFORM@
PYTHON_PREFIX = @PYTHON_PREFIX@
PYTHON_VERSION = @PYTHON_VERSION@
PY_TEST = @PY_TEST@
RANLIB = @RANLIB@
RTLIB = @RTLIB@
RUBYGEMDIR = @RUBYGEMDIR@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
SOCKLIB = @SOCKLIB@
STRIP = @STRIP@
TOX = @TOX@
UNWINDLIB = @UNWINDLIB@
VERSION = @VERSION@
YACC = @YACC@
YFLAGS = @YFLAGS@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
aikgen_plugins = @aikgen_plugins@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
attest_plugins = @attest_plugins@
bindir = @bindir@
botan_CFLAGS = @botan_CFLAGS@
botan_LIBS = @botan_LIBS@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
c_plugins = @c_plugins@
charon_natt_port = @charon_natt_port@
charon_plugins = @charon_plugins@
charon_udp_port = @charon_udp_port@
clearsilver_LIBS = @clearsilver_LIBS@
cmd_plugins = @cmd_plugins@
datadir = @datadir@
datarootdir = @datarootdir@
dbusdatadir = @dbusdatadir@
dbuspolicydir = @dbuspolicydir@
dev_headers = @dev_headers@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
fips_mode = @fips_mode@
fuzz_plugins = @fuzz_plugins@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
imcvdir = @imcvdir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
ipsec_script = @ipsec_script@
ipsec_script_upper = @ipsec_script_upper@
ipsecdir = @ipsecdir@
ipsecgroup = @ipsecgroup@
ipseclibdir = @ipseclibdir@
ipsecuser = @ipsecuser@
json_CFLAGS = @json_CFLAGS@
json_LIBS = @json_LIBS@
libdir = @libdir@
libexecdir = @libexecdir@
libfuzzer = @libfuzzer@
libiptc_CFLAGS = @libiptc_CFLAGS@
libiptc_LIBS = @libiptc_LIBS@
linux_headers = @linux_headers@
localedir = @localedir@
localstatedir = @localstatedir@
manager_plugins = @manager_plugins@
mandir = @mandir@
medsrv_plugins = @medsrv_plugins@
mkdir_p = @mkdir_p@
nm_CFLAGS = @nm_CFLAGS@
nm_LIBS = @nm_LIBS@
nm_ca_dir = @nm_ca_dir@
nm_plugins = @nm_plugins@
oldincludedir = @oldincludedir@
p_plugins = @p_plugins@
pcsclite_CFLAGS = @pcsclite_CFLAGS@
pcsclite_LIBS = @pcsclite_LIBS@
pdfdir = @pdfdir@
piddir = @piddir@
pkgpyexecdir = @pkgpyexecdir@
pkgpythondir = @pkgpythondir@
pki_plugins = @pki_plugins@
plugindir = @plugindir@
pool_plugins = @pool_plugins@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
pyexecdir = @pyexecdir@
pythondir = @pythondir@
random_device = @random_device@
resolv_conf = @resolv_conf@
routing_table = @routing_table@
routing_table_prio = @routing_table_prio@
runstatedir = @runstatedir@
s_plugins = @s_plugins@
sbindir = @sbindir@
scepclient_plugins = @scepclient_plugins@
scripts_plugins = @scripts_plugins@
sharedstatedir = @sharedstatedir@
soup_CFLAGS = @soup_CFLAGS@
soup_LIBS = @soup_LIBS@
srcdir = @srcdir@
starter_plugins = @starter_plugins@
strongswan_conf = @strongswan_conf@
strongswan_options = @strongswan_options@
swanctldir = @swanctldir@
sysconfdir = @sysconfdir@
systemd_CFLAGS = @systemd_CFLAGS@
systemd_LIBS = @systemd_LIBS@
systemd_daemon_CFLAGS = @systemd_daemon_CFLAGS@
systemd_daemon_LIBS = @systemd_daemon_LIBS@
systemd_journal_CFLAGS = @systemd_journal_CFLAGS@
systemd_journal_LIBS = @systemd_journal_LIBS@
systemdsystemunitdir = @systemdsystemunitdir@
t_plugins = @t_plugins@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
tss2_CFLAGS = @tss2_CFLAGS@
tss2_LIBS = @tss2_LIBS@
tss2_esys_CFLAGS = @tss2_esys_CFLAGS@
tss2_esys_LIBS = @tss2_esys_LIBS@
tss2_socket_CFLAGS = @tss2_socket_CFLAGS@
tss2_socket_LIBS = @tss2_socket_LIBS@
tss2_sys_CFLAGS = @tss2_sys_CFLAGS@
tss2_sys_LIBS = @tss2_sys_LIBS@
tss2_tabrmd_CFLAGS = @tss2_tabrmd_CFLAGS@
tss2_tabrmd_LIBS = @tss2_tabrmd_LIBS@
urandom_device = @urandom_device@
wolfssl_CFLAGS = @wolfssl_CFLAGS@
wolfssl_LIBS = @wolfssl_LIBS@
xml_CFLAGS = @xml_CFLAGS@
xml_LIBS = @xml_LIBS@
EXTRA_DIST = git-version
AM_CPPFLAGS = \
	-I$(top_srcdir)/src/libstrongswan \
	-I$(top_srcdir)/src/libtls \
	-DPLUGINS="\"${scripts_plugins}\""

@USE_TLS_TRUE@tls_test_SOURCES = tls_test.c
@USE_TLS_TRUE@tls_test_LDADD = $(top_builddir)/src/libstrongswan/libstrongswan.la \
@USE_TLS_TRUE@					$(top_builddir)/src/libtls/libtls.la

bin2array_SOURCES = bin2array.c
bin2sql_SOURCES = bin2sql.c
id2sql_SOURCES = id2sql.c
key2keyid_SOURCES = key2keyid.c
keyid2sql_SOURCES = keyid2sql.c
oid2der_SOURCES = oid2der.c
thread_analysis_SOURCES = thread_analysis.c
dh_speed_SOURCES = dh_speed.c
pubkey_speed_SOURCES = pubkey_speed.c
crypt_burn_SOURCES = crypt_burn.c
hash_burn_SOURCES = hash_burn.c
malloc_speed_SOURCES = malloc_speed.c
fetch_SOURCES = fetch.c
dnssec_SOURCES = dnssec.c
timeattack_SOURCES = timeattack.c
id2sql_LDADD = $(top_builddir)/src/libstrongswan/libstrongswan.la
key2keyid_LDADD = $(top_builddir)/src/libstrongswan/libstrongswan.la
keyid2sql_LDADD = $(top_builddir)/src/libstrongswan/libstrongswan.la
oid2der_LDADD = $(top_builddir)/src/libstrongswan/libstrongswan.la
dh_speed_LDADD = $(top_builddir)/src/libstrongswan/libstrongswan.la $(RTLIB)
pubkey_speed_LDADD = $(top_builddir)/src/libstrongswan/libstrongswan.la $(RTLIB)
crypt_burn_LDADD = $(top_builddir)/src/libstrongswan/libstrongswan.la
hash_burn_LDADD = $(top_builddir)/src/libstrongswan/libstrongswan.la
malloc_speed_LDADD = $(top_builddir)/src/libstrongswan/libstrongswan.la $(RTLIB)
fetch_LDADD = $(top_builddir)/src/libstrongswan/libstrongswan.la
dnssec_LDADD = $(top_builddir)/src/libstrongswan/libstrongswan.la
aes_test_LDADD = $(top_builddir)/src/libstrongswan/libstrongswan.la
settings_test_LDADD = $(top_builddir)/src/libstrongswan/libstrongswan.la
timeattack_LDADD = $(top_builddir)/src/libstrongswan/libstrongswan.la $(RTLIB)
all: all-am

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu scripts/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu scripts/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

clean-noinstPROGRAMS:
	@list='$(noinst_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list

aes-test$(EXEEXT): $(aes_test_OBJECTS) $(aes_test_DEPENDENCIES) $(EXTRA_aes_test_DEPENDENCIES) 
	@rm -f aes-test$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(aes_test_OBJECTS) $(aes_test_LDADD) $(LIBS)

bin2array$(EXEEXT): $(bin2array_OBJECTS) $(bin2array_DEPENDENCIES) $(EXTRA_bin2array_DEPENDENCIES) 
	@rm -f bin2array$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(bin2array_OBJECTS) $(bin2array_LDADD) $(LIBS)

bin2sql$(EXEEXT): $(bin2sql_OBJECTS) $(bin2sql_DEPENDENCIES) $(EXTRA_bin2sql_DEPENDENCIES) 
	@rm -f bin2sql$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(bin2sql_OBJECTS) $(bin2sql_LDADD) $(LIBS)

crypt_burn$(EXEEXT): $(crypt_burn_OBJECTS) $(crypt_burn_DEPENDENCIES) $(EXTRA_crypt_burn_DEPENDENCIES) 
	@rm -f crypt_burn$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(crypt_burn_OBJECTS) $(crypt_burn_LDADD) $(LIBS)

dh_speed$(EXEEXT): $(dh_speed_OBJECTS) $(dh_speed_DEPENDENCIES) $(EXTRA_dh_speed_DEPENDENCIES) 
	@rm -f dh_speed$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(dh_speed_OBJECTS) $(dh_speed_LDADD) $(LIBS)

dnssec$(EXEEXT): $(dnssec_OBJECTS) $(dnssec_DEPENDENCIES) $(EXTRA_dnssec_DEPENDENCIES) 
	@rm -f dnssec$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(dnssec_OBJECTS) $(dnssec_LDADD) $(LIBS)

fetch$(EXEEXT): $(fetch_OBJECTS) $(fetch_DEPENDENCIES) $(EXTRA_fetch_DEPENDENCIES) 
	@rm -f fetch$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(fetch_OBJECTS) $(fetch_LDADD) $(LIBS)

hash_burn$(EXEEXT): $(hash_burn_OBJECTS) $(hash_burn_DEPENDENCIES) $(EXTRA_hash_burn_DEPENDENCIES) 
	@rm -f hash_burn$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(hash_burn_OBJECTS) $(hash_burn_LDADD) $(LIBS)

id2sql$(EXEEXT): $(id2sql_OBJECTS) $(id2sql_DEPENDENCIES) $(EXTRA_id2sql_DEPENDENCIES) 
	@rm -f id2sql$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(id2sql_OBJECTS) $(id2sql_LDADD) $(LIBS)

key2keyid$(EXEEXT): $(key2keyid_OBJECTS) $(key2keyid_DEPENDENCIES) $(EXTRA_key2keyid_DEPENDENCIES) 
	@rm -f key2keyid$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(key2keyid_OBJECTS) $(key2keyid_LDADD) $(LIBS)

keyid2sql$(EXEEXT): $(keyid2sql_OBJECTS) $(keyid2sql_DEPENDENCIES) $(EXTRA_keyid2sql_DEPENDENCIES) 
	@rm -f keyid2sql$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(keyid2sql_OBJECTS) $(keyid2sql_LDADD) $(LIBS)

malloc_speed$(EXEEXT): $(malloc_speed_OBJECTS) $(malloc_speed_DEPENDENCIES) $(EXTRA_malloc_speed_DEPENDENCIES) 
	@rm -f malloc_speed$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(malloc_speed_OBJECTS) $(malloc_speed_LDADD) $(LIBS)

oid2der$(EXEEXT): $(oid2der_OBJECTS) $(oid2der_DEPENDENCIES) $(EXTRA_oid2der_DEPENDENCIES) 
	@rm -f oid2der$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(oid2der_OBJECTS) $(oid2der_LDADD) $(LIBS)

pubkey_speed$(EXEEXT): $(pubkey_speed_OBJECTS) $(pubkey_speed_DEPENDENCIES) $(EXTRA_pubkey_speed_DEPENDENCIES) 
	@rm -f pubkey_speed$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(pubkey_speed_OBJECTS) $(pubkey_speed_LDADD) $(LIBS)

settings-test$(EXEEXT): $(settings_test_OBJECTS) $(settings_test_DEPENDENCIES) $(EXTRA_settings_test_DEPENDENCIES) 
	@rm -f settings-test$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(settings_test_OBJECTS) $(settings_test_LDADD) $(LIBS)

thread_analysis$(EXEEXT): $(thread_analysis_OBJECTS) $(thread_analysis_DEPENDENCIES) $(EXTRA_thread_analysis_DEPENDENCIES) 
	@rm -f thread_analysis$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(thread_analysis_OBJECTS) $(thread_analysis_LDADD) $(LIBS)

timeattack$(EXEEXT): $(timeattack_OBJECTS) $(timeattack_DEPENDENCIES) $(EXTRA_timeattack_DEPENDENCIES) 
	@rm -f timeattack$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(timeattack_OBJECTS) $(timeattack_LDADD) $(LIBS)

tls_test$(EXEEXT): $(tls_test_OBJECTS) $(tls_test_DEPENDENCIES) $(EXTRA_tls_test_DEPENDENCIES) 
	@rm -f tls_test$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(tls_test_OBJECTS) $(tls_test_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/aes-test.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/bin2array.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/bin2sql.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/crypt_burn.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/dh_speed.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/dnssec.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/fetch.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/hash_burn.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/id2sql.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/key2keyid.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/keyid2sql.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/malloc_speed.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/oid2der.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/pubkey_speed.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/settings-test.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/thread_analysis.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/timeattack.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/tls_test.Po@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCC_TRUE@	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LTCOMPILE) -c -o $@ $<

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(PROGRAMS)
installdirs:
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-libtool clean-noinstPROGRAMS \
	mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/aes-test.Po
	-rm -f ./$(DEPDIR)/bin2array.Po
	-rm -f ./$(DEPDIR)/bin2sql.Po
	-rm -f ./$(DEPDIR)/crypt_burn.Po
	-rm -f ./$(DEPDIR)/dh_speed.Po
	-rm -f ./$(DEPDIR)/dnssec.Po
	-rm -f ./$(DEPDIR)/fetch.Po
	-rm -f ./$(DEPDIR)/hash_burn.Po
	-rm -f ./$(DEPDIR)/id2sql.Po
	-rm -f ./$(DEPDIR)/key2keyid.Po
	-rm -f ./$(DEPDIR)/keyid2sql.Po
	-rm -f ./$(DEPDIR)/malloc_speed.Po
	-rm -f ./$(DEPDIR)/oid2der.Po
	-rm -f ./$(DEPDIR)/pubkey_speed.Po
	-rm -f ./$(DEPDIR)/settings-test.Po
	-rm -f ./$(DEPDIR)/thread_analysis.Po
	-rm -f ./$(DEPDIR)/timeattack.Po
	-rm -f ./$(DEPDIR)/tls_test.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/aes-test.Po
	-rm -f ./$(DEPDIR)/bin2array.Po
	-rm -f ./$(DEPDIR)/bin2sql.Po
	-rm -f ./$(DEPDIR)/crypt_burn.Po
	-rm -f ./$(DEPDIR)/dh_speed.Po
	-rm -f ./$(DEPDIR)/dnssec.Po
	-rm -f ./$(DEPDIR)/fetch.Po
	-rm -f ./$(DEPDIR)/hash_burn.Po
	-rm -f ./$(DEPDIR)/id2sql.Po
	-rm -f ./$(DEPDIR)/key2keyid.Po
	-rm -f ./$(DEPDIR)/keyid2sql.Po
	-rm -f ./$(DEPDIR)/malloc_speed.Po
	-rm -f ./$(DEPDIR)/oid2der.Po
	-rm -f ./$(DEPDIR)/pubkey_speed.Po
	-rm -f ./$(DEPDIR)/settings-test.Po
	-rm -f ./$(DEPDIR)/thread_analysis.Po
	-rm -f ./$(DEPDIR)/timeattack.Po
	-rm -f ./$(DEPDIR)/tls_test.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am:

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am clean \
	clean-generic clean-libtool clean-noinstPROGRAMS cscopelist-am \
	ctags ctags-am distclean distclean-compile distclean-generic \
	distclean-libtool distclean-tags distdir dvi dvi-am html \
	html-am info info-am install install-am install-data \
	install-data-am install-dvi install-dvi-am install-exec \
	install-exec-am install-html install-html-am install-info \
	install-info-am install-man install-pdf install-pdf-am \
	install-ps install-ps-am install-strip installcheck \
	installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags tags-am uninstall uninstall-am

.PRECIOUS: Makefile


key2keyid.o :	$(top_builddir)/config.status

keyid2sql.o :	$(top_builddir)/config.status

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
