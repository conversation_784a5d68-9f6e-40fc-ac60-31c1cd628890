eap-ttls {

    # Maximum size of an EAP-TTLS packet.
    # fragment_size = 1024

    # Include length in non-fragmented EAP-TTLS packets.
    # include_length = yes

    # Whether to load the plugin. Can also be an integer to increase the
    # priority of this plugin.
    load = yes

    # Maximum number of processed EAP-TTLS packets (0 = no limit).
    # max_message_count = 32

    # Phase2 EAP client authentication method.
    # phase2_method = md5

    # Phase2 EAP Identity request piggybacked by server onto TLS Finished
    # message.
    # phase2_piggyback = no

    # Start phase2 EAP TNC protocol after successful client authentication.
    # phase2_tnc = no

    # Phase2 EAP TNC transport protocol (pt as IETF standard or legacy tnc)
    # phase2_tnc_method = pt

    # Request peer authentication based on a client certificate.
    # request_peer_auth = no

}

