/*
 * strongSwan GMALG AF_ALG接口完整实现
 * 通过Linux内核AF_ALG接口调用国密算法
 * 完全替换原有的用户空间实现，提供高性能的内核算法支持
 */

#include <sys/socket.h>
#include <linux/if_alg.h>
#include <unistd.h>
#include <string.h>
#include <errno.h>
#include <stdlib.h>
#include <stdio.h>
#include <fcntl.h>
#include <stdint.h>

#include <library.h>  /* strongSwan库头文件 */
#include <utils/utils.h>  /* 包含字节序转换函数 */

#include "gmalg_af_alg.h"
#include "gmalg.h"

/* 调试开关 */
#define GMALG_DEBUG 1

/* 使用头文件中定义的gmalg_debug宏，避免重复定义 */
#ifdef GMALG_DEBUG_ENABLED
#undef gmalg_debug
#define gmalg_debug(fmt, ...) printf("[GMALG] " fmt "\n", ##__VA_ARGS__)
#else
/* 如果头文件中没有启用调试，这里提供本地实现 */
#if GMALG_DEBUG
#define gmalg_debug(fmt, ...) printf("[GMALG] " fmt "\n", ##__VA_ARGS__)
#else
#define gmalg_debug(fmt, ...)
#endif
#endif

/* 全局状态管理 */
bool g_gmalg_initialized = false;
mutex_t *g_gmalg_mutex = NULL;

/* 全局哈希上下文 - 使用头文件中定义的结构体 */
struct gmalg_hash_context g_hash_ctx = {0};

/* GMALG设备句柄结构体 */
typedef struct gmalg_device_handle_st {
    int sm2_fd;            /* SM2算法socket描述符 */
    int sm3_fd;            /* SM3算法socket描述符 */
    int sm4_fd;            /* SM4算法socket描述符 */
    bool initialized;      /* 是否已初始化 */
    uint8_t sm2_private_key[32];  /* 当前SM2私钥 */
    uint8_t sm4_key[16];          /* 当前SM4密钥 */
    bool sm2_key_set;             /* SM2密钥是否已设置 */
    bool sm4_key_set;             /* SM4密钥是否已设置 */
} gmalg_device_handle;

/* 错误处理宏 */
#define CHECK_HANDLE(h) do { \
    if (!(h) || !(h)->initialized) { \
        gmalg_debug("设备句柄无效"); \
        return -1; \
    } \
} while(0)

#define CHECK_POINTER(p) do { \
    if (!(p)) { \
        gmalg_debug("指针参数为空"); \
        return -1; \
    } \
} while(0)

/* 创建AF_ALG socket连接 */
int create_alg_socket(const char *type, const char *name)
{
    struct sockaddr_alg sa = {
        .salg_family = AF_ALG,
    };
    int sockfd, opfd;

    /* 复制算法类型和名称 */
    strncpy((char *)sa.salg_type, type, sizeof(sa.salg_type) - 1);
    strncpy((char *)sa.salg_name, name, sizeof(sa.salg_name) - 1);

    /* 创建AF_ALG socket */
    sockfd = socket(AF_ALG, SOCK_SEQPACKET, 0);
    if (sockfd < 0) {
        gmalg_debug("创建AF_ALG socket失败: %s", strerror(errno));
        return -1;
    }

    /* 绑定算法 */
    if (bind(sockfd, (struct sockaddr *)&sa, sizeof(sa)) < 0) {
        gmalg_debug("绑定算法%s-%s失败: %s", type, name, strerror(errno));
        close(sockfd);
        return -1;
    }

    /* 创建操作socket */
    opfd = accept(sockfd, NULL, 0);
    if (opfd < 0) {
        gmalg_debug("创建操作socket失败: %s", strerror(errno));
        close(sockfd);
        return -1;
    }

    close(sockfd);
    gmalg_debug("成功创建%s-%s算法连接，fd=%d", type, name, opfd);
    return opfd;
}

/* 发送AF_ALG请求并接收响应 */
int send_alg_request(int fd, const void *req_data, size_t req_len, 
                           void **resp_data, size_t *resp_len)
{
    struct msghdr msg = {0};
    struct iovec iov = {0};
    int ret;

    /* 分配响应缓冲区 */
    *resp_data = malloc(8192);  /* 足够大的缓冲区 */
    if (!*resp_data) {
        gmalg_debug("分配响应缓冲区失败");
        return -ENOMEM;
    }

    /* 设置发送消息结构 */
    iov.iov_base = (void *)req_data;
    iov.iov_len = req_len;
    msg.msg_iov = &iov;
    msg.msg_iovlen = 1;

    /* 发送请求 */
    ret = sendmsg(fd, &msg, 0);
    if (ret < 0) {
        gmalg_debug("发送AF_ALG请求失败: %s", strerror(errno));
        free(*resp_data);
        *resp_data = NULL;
        return ret;
    }

    gmalg_debug("成功发送请求，大小=%d字节", ret);

    /* 接收响应 */
    iov.iov_base = *resp_data;
    iov.iov_len = 8192;
    ret = recvmsg(fd, &msg, 0);
    if (ret < 0) {
        gmalg_debug("接收AF_ALG响应失败: %s", strerror(errno));
        free(*resp_data);
        *resp_data = NULL;
        return ret;
    }

    *resp_len = ret;
    gmalg_debug("成功接收响应，大小=%zu字节", *resp_len);
    return 0;
}

/* 设置socket选项 */
int set_alg_key(int fd, const void *key, size_t key_len)
{
    if (setsockopt(fd, SOL_ALG, ALG_SET_KEY, key, key_len) < 0) {
        gmalg_debug("设置密钥失败: %s", strerror(errno));
        return -1;
    }
    gmalg_debug("成功设置密钥，长度=%zu字节", key_len);
    return 0;
}

/* 内存安全清除函数 */
void secure_memzero(void *ptr, size_t size)
{
    if (ptr && size > 0) {
        volatile char *p = (volatile char *)ptr;
        while (size--)
            *p++ = 0;
    }
}

/* ============ 全局初始化和清理函数 ============ */

/*
 * 描述： 初始化全局GMALG接口
 * 返回值： 0 成功，非0 失败
 */
int gmalg_global_init(void)
{
    gmalg_debug("GMALG全局初始化开始");

    if (!g_gmalg_mutex) {
        g_gmalg_mutex = mutex_create(MUTEX_TYPE_DEFAULT);
        if (!g_gmalg_mutex) {
            gmalg_debug("创建全局mutex失败");
            return -1;
        }
    }

    return 0;
}

/*
 * 描述： 清理全局GMALG接口
 */
void gmalg_global_cleanup(void)
{
    if (g_gmalg_mutex) {
        g_gmalg_mutex->destroy(g_gmalg_mutex);
        g_gmalg_mutex = NULL;
    }

    if (g_hash_ctx.buffer) {
        free(g_hash_ctx.buffer);
        g_hash_ctx.buffer = NULL;
    }

    g_gmalg_initialized = false;
    gmalg_debug("GMALG全局清理完成");
}

/* ============ GMALG接口实现 ============ */

/*
 * 描述： 库测试函数
 * 参数： 无参数
 * 返回值： 0 成功，非0 失败
 */
int GMALG_LibTest(void)
{
    int fd;
    gmalg_debug("GMALG AF_ALG库测试开始");

    /* 测试SM2算法可用性 */
    fd = create_alg_socket("akcipher", "sm2");
    if (fd < 0) {
        gmalg_debug("SM2算法不可用");
        return -1;
    }
    close(fd);
    gmalg_debug("SM2算法测试通过");

    /* 测试SM3算法可用性 */
    fd = create_alg_socket("hash", "sm3");
    if (fd < 0) {
        gmalg_debug("SM3算法不可用");
        return -1;
    }
    close(fd);
    gmalg_debug("SM3算法测试通过");

    /* 测试SM4算法可用性 */
    fd = create_alg_socket("skcipher", "sm4");
    if (fd < 0) {
        gmalg_debug("SM4算法不可用");
        return -1;
    }
    close(fd);
    gmalg_debug("SM4算法测试通过");

    gmalg_debug("GMALG AF_ALG库测试成功完成");
    return 0;
}

/*
 * 描述： 打开密码设备
 * 参数： phDeviceHandle[out] 返回设备句柄
 * 返回值： 0 成功，非0 失败
 */
int GMALG_OpenDevice(void **phDeviceHandle)
{
    gmalg_device_handle *handle;

    CHECK_POINTER(phDeviceHandle);

    if (g_gmalg_mutex) g_gmalg_mutex->lock(g_gmalg_mutex);

    handle = malloc(sizeof(gmalg_device_handle));
    if (!handle) {
        gmalg_debug("分配设备句柄内存失败");
        if (g_gmalg_mutex) g_gmalg_mutex->unlock(g_gmalg_mutex);
        return -1;
    }

    memset(handle, 0, sizeof(gmalg_device_handle));

    /* 创建SM2算法连接 */
    handle->sm2_fd = create_alg_socket("akcipher", "sm2");
    if (handle->sm2_fd < 0) {
        gmalg_debug("创建SM2连接失败");
        free(handle);
        if (g_gmalg_mutex) g_gmalg_mutex->unlock(g_gmalg_mutex);
        return -1;
    }

    /* 创建SM3算法连接 */
    handle->sm3_fd = create_alg_socket("hash", "sm3");
    if (handle->sm3_fd < 0) {
        gmalg_debug("创建SM3连接失败");
        close(handle->sm2_fd);
        free(handle);
        if (g_gmalg_mutex) g_gmalg_mutex->unlock(g_gmalg_mutex);
        return -1;
    }

    /* 创建SM4算法连接 */
    handle->sm4_fd = create_alg_socket("skcipher", "sm4");
    if (handle->sm4_fd < 0) {
        gmalg_debug("创建SM4连接失败");
        close(handle->sm2_fd);
        close(handle->sm3_fd);
        free(handle);
        if (g_gmalg_mutex) g_gmalg_mutex->unlock(g_gmalg_mutex);
        return -1;
    }

    handle->initialized = true;
    *phDeviceHandle = handle;

    if (!g_gmalg_initialized) {
        /* 初始化全局哈希上下文 */
        g_hash_ctx.buffer_capacity = 1024 * 1024;  /* 1MB初始容量 */
        g_hash_ctx.buffer = malloc(g_hash_ctx.buffer_capacity);
        if (!g_hash_ctx.buffer) {
            gmalg_debug("初始化哈希缓冲区失败");
            GMALG_CloseDevice(handle);
            if (g_gmalg_mutex) g_gmalg_mutex->unlock(g_gmalg_mutex);
            return -1;
        }
        g_hash_ctx.buffer_len = 0;
        g_hash_ctx.finalized = false;
        g_gmalg_initialized = true;
    }

    if (g_gmalg_mutex) g_gmalg_mutex->unlock(g_gmalg_mutex);
    gmalg_debug("成功打开GMALG设备");
    return 0;
}

/*
 * 描述： 关闭密码设备，并释放相关资源
 * 参数： hDeviceHandle[in] 已打开的设备句柄
 * 返回值： 0 成功，非0 失败
 */
int GMALG_CloseDevice(void *hDeviceHandle)
{
    gmalg_device_handle *handle = (gmalg_device_handle *)hDeviceHandle;

    if (!handle) {
        gmalg_debug("设备句柄为空");
        return -1;
    }

    if (g_gmalg_mutex) g_gmalg_mutex->lock(g_gmalg_mutex);

    if (handle->initialized) {
        if (handle->sm2_fd >= 0) {
            close(handle->sm2_fd);
            gmalg_debug("关闭SM2连接");
        }
        if (handle->sm3_fd >= 0) {
            close(handle->sm3_fd);
            gmalg_debug("关闭SM3连接");
        }
        if (handle->sm4_fd >= 0) {
            close(handle->sm4_fd);
            gmalg_debug("关闭SM4连接");
        }

        /* 清除敏感数据 */
        secure_memzero(handle->sm2_private_key, sizeof(handle->sm2_private_key));
        secure_memzero(handle->sm4_key, sizeof(handle->sm4_key));
    }

    free(handle);

    if (g_gmalg_mutex) g_gmalg_mutex->unlock(g_gmalg_mutex);
    gmalg_debug("成功关闭GMALG设备");
    return 0;
}

/*
 * 描述： 获取指定长度的随机数
 * 参数： hDeviceHandle[in]  与设备建立的会话句柄
 *        uiLength[in]       欲获取的随机数长度
 *        pucRandom[out]     缓冲区指针，用于存放获取的随机数
 * 返回值： 0 成功，非0 失败
 */
int GMALG_GenerateRandom(void *hDeviceHandle, unsigned int uiLength, unsigned char *pucRandom)
{
    int fd;
    ssize_t ret;

    CHECK_POINTER(pucRandom);

    if (uiLength == 0) {
        gmalg_debug("随机数长度为0");
        return -1;
    }

    /* 使用系统随机数生成器 */
    fd = open("/dev/urandom", O_RDONLY);
    if (fd < 0) {
        gmalg_debug("打开/dev/urandom失败: %s", strerror(errno));
        return -1;
    }

    ret = read(fd, pucRandom, uiLength);
    close(fd);

    if (ret != uiLength) {
        gmalg_debug("读取随机数失败，期望%u字节，实际%ld字节", uiLength, ret);
        return -1;
    }

    gmalg_debug("成功生成%u字节随机数", uiLength);
    return 0;
}

/*
 * 描述： 请求密码设备产生 ECC 密钥对
 * 参数： hDeviceHandle[in]  与设备建立的会话句柄
 *        pucPublicKey[out]  ECC 公钥结构
 *        pucPrivateKey[out] ECC 私钥结构
 * 返回值： 0 成功，非0 失败
 */
int GMALG_GenerateKeyPair_ECC(void *hDeviceHandle, ECCrefPublicKey *pucPublicKey, ECCrefPrivateKey *pucPrivateKey)
{
    gmalg_device_handle *handle = (gmalg_device_handle *)hDeviceHandle;
    struct sm_alg_req *req;
    struct sm_alg_resp *resp;
    void *resp_data;
    size_t resp_len;
    int ret;

    CHECK_HANDLE(handle);
    CHECK_POINTER(pucPublicKey);
    CHECK_POINTER(pucPrivateKey);

    /* 构造密钥生成请求 */
    req = malloc(sizeof(struct sm_alg_req));
    if (!req) {
        gmalg_debug("分配请求内存失败");
        return -1;
    }

    req->operation = ALG_OP_SM2_KEYGEN;
    req->key_len = 0;
    req->data_len = 0;
    req->output_len = 0;
    req->user_id_len = 0;
    req->mode = 0;

    /* 发送密钥生成请求 */
    ret = send_alg_request(handle->sm2_fd, req, sizeof(struct sm_alg_req), &resp_data, &resp_len);
    free(req);

    if (ret != 0) {
        gmalg_debug("发送密钥生成请求失败");
        return -1;
    }

    if (resp_len < sizeof(struct sm_alg_resp)) {
        gmalg_debug("响应数据长度不足");
        free(resp_data);
        return -1;
    }

    resp = (struct sm_alg_resp *)resp_data;
    if (resp->result != 0) {
        gmalg_debug("密钥生成操作失败，错误码=%d", resp->result);
        free(resp_data);
        return -1;
    }

    if (resp->output_len < 97) {  /* 32(私钥) + 65(公钥) */
        gmalg_debug("密钥数据长度不足，期望97字节，实际%u字节", resp->output_len);
        free(resp_data);
        return -1;
    }

    /* 解析私钥（前32字节） */
    pucPrivateKey->bits = htonl(0x100);
    memcpy(pucPrivateKey->K, resp->data, 32);

    /* 解析公钥（后65字节：04 + x + y） */
    if (resp->data[32] != 0x04) {
        gmalg_debug("公钥格式错误，未压缩标志不正确");
        free(resp_data);
        return -1;
    }

    pucPublicKey->bits = htonl(0x100);
    memcpy(pucPublicKey->x, resp->data + 33, 32);
    memcpy(pucPublicKey->y, resp->data + 65, 32);

    /* 保存私钥到句柄中 */
    memcpy(handle->sm2_private_key, pucPrivateKey->K, 32);
    handle->sm2_key_set = true;

    free(resp_data);
    gmalg_debug("成功生成SM2密钥对");
    return 0;
}

/*
 * 描述： 通过私钥生成公钥
 * 参数： hDeviceHandle[in]  与设备建立的会话句柄
 *        pucPrivateKey[in]  ECC 私钥结构
 *        pucPublicKey[out]  ECC 公钥结构
 * 返回值： 0 成功，非0 失败
 */
int GMALG_GeneratePublicKey_ECC(void *hDeviceHandle, ECCrefPrivateKey *pucPrivateKey, ECCrefPublicKey *pucPublicKey)
{
    gmalg_device_handle *handle = (gmalg_device_handle *)hDeviceHandle;
    struct sm_alg_req *req;
    struct sm_alg_resp *resp;
    void *resp_data;
    size_t resp_len;
    int ret;

    CHECK_HANDLE(handle);
    CHECK_POINTER(pucPrivateKey);
    CHECK_POINTER(pucPublicKey);

    /* 构造公钥生成请求 */
    req = malloc(sizeof(struct sm_alg_req) + 32);
    if (!req) {
        gmalg_debug("分配请求内存失败");
        return -1;
    }

    req->operation = ALG_OP_SM2_PUBKEY_GEN;  /* 新增操作类型 */
    req->key_len = 32;
    req->data_len = 0;
    req->output_len = 65;
    req->user_id_len = 0;
    req->mode = 0;

    /* 复制私钥 */
    memcpy(req->data, pucPrivateKey->K, 32);

    /* 发送公钥生成请求 */
    ret = send_alg_request(handle->sm2_fd, req, sizeof(struct sm_alg_req) + 32, &resp_data, &resp_len);
    free(req);

    if (ret != 0) {
        gmalg_debug("发送公钥生成请求失败");
        return -1;
    }

    resp = (struct sm_alg_resp *)resp_data;
    if (resp->result != 0 || resp->output_len != 65) {
        gmalg_debug("公钥生成操作失败");
        free(resp_data);
        return -1;
    }

    /* 解析公钥 */
    if (resp->data[0] != 0x04) {
        gmalg_debug("公钥格式错误");
        free(resp_data);
        return -1;
    }

    pucPublicKey->bits = htonl(0x100);
    memcpy(pucPublicKey->x, resp->data + 1, 32);
    memcpy(pucPublicKey->y, resp->data + 33, 32);

    free(resp_data);
    gmalg_debug("成功生成SM2公钥");
    return 0;
}

/*
 * 描述： 椭圆曲线点乘运算
 * 参数： hDeviceHandle[in]  与设备建立的会话句柄
 *        pucG[in]           ECC 基点
 *        pucK[in]           ECC 倍数
 *        pucP[out]          ECC 倍点后的值
 * 返回值： 0 成功，非0 失败
 */
int GMALG_pointMul_ECC(void *hDeviceHandle, ECCrefPublicKey *pucG, ECCrefPrivateKey *pucK, ECCrefPublicKey *pucP)
{
    gmalg_device_handle *handle = (gmalg_device_handle *)hDeviceHandle;
    struct sm_alg_req *req;
    struct sm_alg_resp *resp;
    void *resp_data;
    size_t resp_len;
    size_t req_size;
    int ret;

    CHECK_HANDLE(handle);
    CHECK_POINTER(pucG);
    CHECK_POINTER(pucK);
    CHECK_POINTER(pucP);

    /* 构造点乘请求：65字节公钥点 + 32字节私钥 */
    req_size = sizeof(struct sm_alg_req) + 65 + 32;
    req = malloc(req_size);
    if (!req) {
        gmalg_debug("分配请求内存失败");
        return -1;
    }

    req->operation = ALG_OP_SM2_POINT_MUL;  /* 新增操作类型 */
    req->key_len = 32;
    req->data_len = 65;
    req->output_len = 65;
    req->user_id_len = 0;
    req->mode = 0;

    /* 组装基点（未压缩格式） */
    req->data[0] = 0x04;
    memcpy(req->data + 1, pucG->x, 32);
    memcpy(req->data + 33, pucG->y, 32);

    /* 复制倍数（私钥） */
    memcpy(req->data + 65, pucK->K, 32);

    /* 发送点乘请求 */
    ret = send_alg_request(handle->sm2_fd, req, req_size, &resp_data, &resp_len);
    free(req);

    if (ret != 0) {
        gmalg_debug("发送点乘请求失败");
        return -1;
    }

    resp = (struct sm_alg_resp *)resp_data;
    if (resp->result != 0 || resp->output_len != 65) {
        gmalg_debug("点乘运算失败");
        free(resp_data);
        return -1;
    }

    /* 解析结果点 */
    if (resp->data[0] != 0x04) {
        gmalg_debug("结果点格式错误");
        free(resp_data);
        return -1;
    }

    pucP->bits = htonl(0x100);
    memcpy(pucP->x, resp->data + 1, 32);
    memcpy(pucP->y, resp->data + 33, 32);

    free(resp_data);
    gmalg_debug("成功完成SM2点乘运算");
    return 0;
}

/*
 * 描述： 使用外部 ECC 私钥对数据进行签名运算
 * 参数： hDeviceHandle[in]  与设备建立的会话句柄
 *        pucPrivateKey[in]  外部 ECC 私钥结构
 *        pucData[in]        缓冲区指针，用于存放外部输入的数据
 *        uiDataLength[in]   输入的数据长度
 *        pucSignature[out]  缓冲区指针，用于存放输出的签名值数据
 * 返回值： 0 成功，非0 失败
 */
int GMALG_ExternalSign_ECC(void *hDeviceHandle, ECCrefPrivateKey *pucPrivateKey, 
                           unsigned char *pucData, unsigned int uiDataLength, ECCSignature *pucSignature)
{
    gmalg_device_handle *handle = (gmalg_device_handle *)hDeviceHandle;
    struct sm_alg_req *req;
    struct sm_alg_resp *resp;
    void *resp_data;
    size_t resp_len;
    size_t req_size;
    const char *default_user_id = "1234567812345678";
    int ret;

    CHECK_HANDLE(handle);
    CHECK_POINTER(pucPrivateKey);
    CHECK_POINTER(pucData);
    CHECK_POINTER(pucSignature);

    if (uiDataLength == 0) {
        gmalg_debug("签名数据长度为0");
        return -1;
    }

    /* 计算请求大小：数据 + 默认用户ID */
    req_size = sizeof(struct sm_alg_req) + uiDataLength + 16;
    req = malloc(req_size);
    if (!req) {
        gmalg_debug("分配请求内存失败");
        return -1;
    }

    /* 构造签名请求 */
    req->operation = ALG_OP_SM2_SIGN;
    req->key_len = 32;
    req->data_len = uiDataLength;
    req->output_len = 64;
    req->user_id_len = 16;
    req->mode = 0;

    /* 复制数据和默认用户ID */
    memcpy(req->data, pucData, uiDataLength);
    memcpy(req->data + uiDataLength, default_user_id, 16);

    /* 设置私钥 */
    if (set_alg_key(handle->sm2_fd, pucPrivateKey->K, 32) != 0) {
        gmalg_debug("设置SM2私钥失败");
        free(req);
        return -1;
    }

    /* 发送签名请求 */
    ret = send_alg_request(handle->sm2_fd, req, req_size, &resp_data, &resp_len);
    free(req);

    if (ret != 0) {
        gmalg_debug("发送签名请求失败");
        return -1;
    }

    resp = (struct sm_alg_resp *)resp_data;
    if (resp->result != 0) {
        gmalg_debug("签名操作失败，错误码=%d", resp->result);
        free(resp_data);
        return -1;
    }

    if (resp->output_len != 64) {
        gmalg_debug("签名数据长度错误，期望64字节，实际%u字节", resp->output_len);
        free(resp_data);
        return -1;
    }

    /* 提取签名结果 */
    memcpy(pucSignature->r, resp->data, 32);
    memcpy(pucSignature->s, resp->data + 32, 32);

    free(resp_data);
    gmalg_debug("成功完成SM2签名，数据长度=%u字节", uiDataLength);
    return 0;
}

/*
 * 描述： 使用外部 ECC 公钥对 ECC 签名值进行验证运算
 * 参数： hDeviceHandle[in]  与设备建立的会话句柄
 *        pucPublicKey[in]   外部 ECC 公钥结构
 *        pucDataInput[in]   缓冲区指针，用于存放外部输入的数据
 *        uiInputLength[in]  输入的数据长度
 *        pucSignature[in]   缓冲区指针，用于存放输入的签名值数据
 * 返回值： 0 成功，非0 失败
 */
int GMALG_ExternalVerify_ECC(void *hDeviceHandle, ECCrefPublicKey *pucPublicKey,
                             unsigned char *pucDataInput, unsigned int uiInputLength, ECCSignature *pucSignature)
{
    gmalg_device_handle *handle = (gmalg_device_handle *)hDeviceHandle;
    struct sm_alg_req *req;
    struct sm_alg_resp *resp;
    void *resp_data;
    size_t resp_len;
    size_t req_size;
    const char *default_user_id = "1234567812345678";
    int ret;

    CHECK_HANDLE(handle);
    CHECK_POINTER(pucPublicKey);
    CHECK_POINTER(pucDataInput);
    CHECK_POINTER(pucSignature);

    if (uiInputLength == 0) {
        gmalg_debug("验证数据长度为0");
        return -1;
    }

    /* 计算请求大小：公钥(65) + 签名(64) + 数据 + 用户ID(16) */
    req_size = sizeof(struct sm_alg_req) + 65 + 64 + uiInputLength + 16;
    req = malloc(req_size);
    if (!req) {
        gmalg_debug("分配请求内存失败");
        return -1;
    }

    /* 构造验证请求 */
    req->operation = ALG_OP_SM2_VERIFY;
    req->key_len = 0;
    req->data_len = uiInputLength;
    req->output_len = 0;
    req->user_id_len = 16;
    req->mode = 0;

    /* 组装公钥（未压缩格式） */
    req->data[0] = 0x04;
    memcpy(req->data + 1, pucPublicKey->x, 32);
    memcpy(req->data + 33, pucPublicKey->y, 32);

    /* 组装签名 */
    memcpy(req->data + 65, pucSignature->r, 32);
    memcpy(req->data + 97, pucSignature->s, 32);

    /* 组装数据和用户ID */
    memcpy(req->data + 129, pucDataInput, uiInputLength);
    memcpy(req->data + 129 + uiInputLength, default_user_id, 16);

    /* 发送验证请求 */
    ret = send_alg_request(handle->sm2_fd, req, req_size, &resp_data, &resp_len);
    free(req);

    if (ret != 0) {
        gmalg_debug("发送验证请求失败");
        return -1;
    }

    resp = (struct sm_alg_resp *)resp_data;
    ret = resp->result;

    free(resp_data);
    
    if (ret == 0) {
        gmalg_debug("SM2签名验证成功");
    } else {
        gmalg_debug("SM2签名验证失败，错误码=%d", ret);
    }
    
    return ret;
}

/*
 * 描述： 使用外部 ECC 公钥对数据进行加密运算
 * 参数： hDeviceHandle[in]  与设备建立的会话句柄
 *        pucPublicKey[in]   外部 ECC 公钥结构
 *        pucData[in]        缓冲区指针，用于存放外部输入的数据
 *        uiDataLength[in]   输入的数据长度
 *        pucEncData[out]    缓冲区指针，用于存放输出的数据密文
 * 返回值： 0 成功，非0 失败
 */
int GMALG_ExternalEncrypt_ECC(void *hDeviceHandle, ECCrefPublicKey *pucPublicKey,
                              unsigned char *pucData, unsigned int uiDataLength, ECCCipher *pucEncData)
{
    gmalg_device_handle *handle = (gmalg_device_handle *)hDeviceHandle;
    struct sm_alg_req *req;
    struct sm_alg_resp *resp;
    void *resp_data;
    size_t resp_len;
    size_t req_size;
    int ret;

    CHECK_HANDLE(handle);
    CHECK_POINTER(pucPublicKey);
    CHECK_POINTER(pucData);
    CHECK_POINTER(pucEncData);

    if (uiDataLength == 0 || uiDataLength > 255) {
        gmalg_debug("加密数据长度无效: %u", uiDataLength);
        return -1;
    }

    /* 计算请求大小：公钥(65) + 明文数据 */
    req_size = sizeof(struct sm_alg_req) + 65 + uiDataLength;
    req = malloc(req_size);
    if (!req) {
        gmalg_debug("分配请求内存失败");
        return -1;
    }

    /* 构造加密请求 */
    req->operation = ALG_OP_SM2_ENCRYPT;
    req->key_len = 0;
    req->data_len = uiDataLength;
    req->output_len = 0;
    req->user_id_len = 0;
    req->mode = 0;

    /* 组装公钥（未压缩格式） */
    req->data[0] = 0x04;
    memcpy(req->data + 1, pucPublicKey->x, 32);
    memcpy(req->data + 33, pucPublicKey->y, 32);

    /* 组装明文数据 */
    memcpy(req->data + 65, pucData, uiDataLength);

    /* 发送加密请求 */
    ret = send_alg_request(handle->sm2_fd, req, req_size, &resp_data, &resp_len);
    free(req);

    if (ret != 0) {
        gmalg_debug("发送加密请求失败");
        return -1;
    }

    resp = (struct sm_alg_resp *)resp_data;
    if (resp->result != 0) {
        gmalg_debug("加密操作失败，错误码=%d", resp->result);
        free(resp_data);
        return -1;
    }

    if (resp->output_len < 97) {  /* 最小密文长度：1+64+32 */
        gmalg_debug("密文长度异常: %u", resp->output_len);
        free(resp_data);
        return -1;
    }

    /* 解析密文数据到ECCCipher结构 */
    if (resp->output_len < 97) {  /* 最小密文长度：1+64+32 */
        gmalg_debug("密文长度异常: %u", resp->output_len);
        free(resp_data);
        return -1;
    }

    /* 解析密文结构：04 + x(32) + y(32) + M(32) + L(4) + C(变长) */
    if (resp->data[0] != 0x04) {
        gmalg_debug("密文格式错误");
        free(resp_data);
        return -1;
    }

    /* 填充ECCCipher结构 */
    memcpy(pucEncData->x, resp->data + 1, 32);
    memcpy(pucEncData->y, resp->data + 33, 32);
    memcpy(pucEncData->M, resp->data + 65, 32);

    /* 计算密文长度 */
    pucEncData->L = resp->output_len - 97;
    if (pucEncData->L > 0 && pucEncData->L <= 32) {
        memcpy(pucEncData->C, resp->data + 97, pucEncData->L);
    }

    free(resp_data);
    gmalg_debug("成功完成SM2加密，明文%u字节，密文%u字节", uiDataLength, resp->output_len);
    return 0;
}

/*
 * 描述： 使用外部 ECC 私钥进行解密运算
 * 参数： hDeviceHandle[in]  与设备建立的会话句柄
 *        pucPrivateKey[in]  外部 ECC 私钥结构
 *        pucEncData[in]     缓冲区指针，用于存放输入的数据密文
 *        uiDataLen[in]      输入的数据密文长度
 *        pucData[out]       缓冲区指针，用于存放输出的数据明文
 * 返回值： 0 成功，非0 失败
 */
int GMALG_ExternalDecrypt_ECC(void *hDeviceHandle, ECCrefPrivateKey *pucPrivateKey,
                              unsigned char *pucEncData, unsigned int uiDataLen, unsigned char *pucData)
{
    gmalg_device_handle *handle = (gmalg_device_handle *)hDeviceHandle;
    struct sm_alg_req *req;
    struct sm_alg_resp *resp;
    void *resp_data;
    size_t resp_len;
    size_t req_size;
    int ret;

    CHECK_HANDLE(handle);
    CHECK_POINTER(pucPrivateKey);
    CHECK_POINTER(pucEncData);
    CHECK_POINTER(pucData);

    if (uiDataLen < 97) {  /* 最小密文长度检查 */
        gmalg_debug("密文长度过短: %u", uiDataLen);
        return -1;
    }

    /* 计算请求大小：密文数据 */
    req_size = sizeof(struct sm_alg_req) + uiDataLen;
    req = malloc(req_size);
    if (!req) {
        gmalg_debug("分配请求内存失败");
        return -1;
    }

    /* 构造解密请求 */
    req->operation = ALG_OP_SM2_DECRYPT;
    req->key_len = 32;
    req->data_len = uiDataLen;
    req->output_len = 0;
    req->user_id_len = 0;
    req->mode = 0;

    /* 组装密文数据 */
    memcpy(req->data, pucEncData, uiDataLen);

    /* 设置私钥 */
    if (set_alg_key(handle->sm2_fd, pucPrivateKey->K, 32) != 0) {
        gmalg_debug("设置SM2私钥失败");
        free(req);
        return -1;
    }

    /* 发送解密请求 */
    ret = send_alg_request(handle->sm2_fd, req, req_size, &resp_data, &resp_len);
    free(req);

    if (ret != 0) {
        gmalg_debug("发送解密请求失败");
        return -1;
    }

    resp = (struct sm_alg_resp *)resp_data;
    if (resp->result != 0) {
        gmalg_debug("解密操作失败，错误码=%d", resp->result);
        free(resp_data);
        return -1;
    }

    /* 复制明文数据 */
    memcpy(pucData, resp->data, resp->output_len);

    free(resp_data);
    gmalg_debug("成功完成SM2解密，密文%u字节，明文%u字节", uiDataLen, resp->output_len);
    return 0;
}

/*
 * 描述： 使用指定的密钥对数据进行对称加密运算
 * 参数： hDeviceHandle[in]     与设备建立的会话句柄
 *        pucKey[in]            指定的密钥
 *        uiAlgID[in]           算法标识，指定对称加密算法
 *        pucIV[in]             缓冲区指针，用于存放输入的 IV 数据
 *        pucData[in]           缓冲区指针，用于存放输入的数据明文
 *        uiDataLength[in]      输入的数据明文长度
 *        pucEncData[out]       缓冲区指针，用于存放输出的数据密文
 *        puiEncDataLength[out] 输出的数据密文长度
 * 返回值： 0 成功，非0 失败
 */
int GMALG_Encrypt(void *hDeviceHandle, void *pucKey, unsigned int uiAlgID,
                  unsigned char *pucIV, unsigned char *pucData, unsigned int uiDataLength,
                  unsigned char *pucEncData, unsigned int *puiEncDataLength)
{
    gmalg_device_handle *handle = (gmalg_device_handle *)hDeviceHandle;
    struct sm_alg_req *req;
    struct sm_alg_resp *resp;
    void *resp_data;
    size_t resp_len;
    size_t req_size;
    const unsigned char null_iv[16] = {0};
    int ret;

    CHECK_HANDLE(handle);
    CHECK_POINTER(pucKey);
    CHECK_POINTER(pucData);
    CHECK_POINTER(pucEncData);

    /* 检查算法类型 */
    switch (uiAlgID) {
    case GMALG_SM1_ECB:
    case GMALG_SM1_CBC:
    case GMALG_SM4_ECB:
    case GMALG_SM4_CBC:
        break;
    default:
        gmalg_debug("不支持的算法类型: 0x%08x", uiAlgID);
        return -1;
    }

    /* 检查数据长度（必须是16字节的倍数） */
    if (uiDataLength == 0 || (uiDataLength % 16) != 0) {
        gmalg_debug("数据长度必须是16字节的倍数: %u", uiDataLength);
        return -1;
    }

    /* 计算请求大小：IV(16) + 明文数据 */
    req_size = sizeof(struct sm_alg_req) + 16 + uiDataLength;
    req = malloc(req_size);
    if (!req) {
        gmalg_debug("分配请求内存失败");
        return -1;
    }

    /* 构造加密请求 */
    req->operation = ALG_OP_SM4_ENCRYPT;
    req->key_len = 16;
    req->data_len = uiDataLength;
    req->output_len = 0;
    req->user_id_len = 0;
    
    /* 设置工作模式 */
    switch (uiAlgID) {
    case GMALG_SM1_ECB:
    case GMALG_SM4_ECB:
        req->mode = SM4_MODE_ECB;
        break;
    case GMALG_SM1_CBC:
    case GMALG_SM4_CBC:
        req->mode = SM4_MODE_CBC;
        break;
    }

    /* 组装IV和数据 */
    memcpy(req->data, pucIV ? pucIV : null_iv, 16);
    memcpy(req->data + 16, pucData, uiDataLength);

    /* 设置密钥 */
    if (set_alg_key(handle->sm4_fd, pucKey, 16) != 0) {
        gmalg_debug("设置SM4密钥失败");
        free(req);
        return -1;
    }

    /* 发送加密请求 */
    ret = send_alg_request(handle->sm4_fd, req, req_size, &resp_data, &resp_len);
    free(req);

    if (ret != 0) {
        gmalg_debug("发送SM4加密请求失败");
        return -1;
    }

    resp = (struct sm_alg_resp *)resp_data;
    if (resp->result != 0) {
        gmalg_debug("SM4加密操作失败，错误码=%d", resp->result);
        free(resp_data);
        return -1;
    }

    /* 复制密文数据 */
    memcpy(pucEncData, resp->data, resp->output_len);
    if (puiEncDataLength) {
        *puiEncDataLength = resp->output_len;
    }

    free(resp_data);
    gmalg_debug("成功完成SM4加密，算法=0x%08x，明文%u字节", uiAlgID, uiDataLength);
    return 0;
}

/*
 * 描述： 使用指定的密钥对数据进行对称解密运算
 * 参数： hDeviceHandle[in]   与设备建立的会话句柄
 *        pucKey[in]          指定的密钥
 *        uiAlgID[in]         算法标识，指定对称加密算法
 *        pucIV[in]           缓冲区指针，用于存放输入的 IV 数据
 *        pucEncData[in]      缓冲区指针，用于存放输入的数据密文
 *        uiEncDataLength[in] 输入的数据密文长度
 *        pucData[out]        缓冲区指针，用于存放输出的数据明文
 *        puiDataLength[out]  输出的数据明文长度
 * 返回值： 0 成功，非0 失败
 */
int GMALG_Decrypt(void *hDeviceHandle, void *pucKey, unsigned int uiAlgID,
                  unsigned char *pucIV, unsigned char *pucEncData, unsigned int uiEncDataLength,
                  unsigned char *pucData, unsigned int *puiDataLength)
{
    gmalg_device_handle *handle = (gmalg_device_handle *)hDeviceHandle;
    struct sm_alg_req *req;
    struct sm_alg_resp *resp;
    void *resp_data;
    size_t resp_len;
    size_t req_size;
    const unsigned char null_iv[16] = {0};
    int ret;

    CHECK_HANDLE(handle);
    CHECK_POINTER(pucKey);
    CHECK_POINTER(pucEncData);
    CHECK_POINTER(pucData);

    /* 检查算法类型 */
    switch (uiAlgID) {
    case GMALG_SM1_ECB:
    case GMALG_SM1_CBC:
    case GMALG_SM4_ECB:
    case GMALG_SM4_CBC:
        break;
    default:
        gmalg_debug("不支持的算法类型: 0x%08x", uiAlgID);
        return -1;
    }

    /* 检查数据长度（必须是16字节的倍数） */
    if (uiEncDataLength == 0 || (uiEncDataLength % 16) != 0) {
        gmalg_debug("密文长度必须是16字节的倍数: %u", uiEncDataLength);
        return -1;
    }

    /* 计算请求大小：IV(16) + 密文数据 */
    req_size = sizeof(struct sm_alg_req) + 16 + uiEncDataLength;
    req = malloc(req_size);
    if (!req) {
        gmalg_debug("分配请求内存失败");
        return -1;
    }

    /* 构造解密请求 */
    req->operation = ALG_OP_SM4_DECRYPT;
    req->key_len = 16;
    req->data_len = uiEncDataLength;
    req->output_len = 0;
    req->user_id_len = 0;
    
    /* 设置工作模式 */
    switch (uiAlgID) {
    case GMALG_SM1_ECB:
    case GMALG_SM4_ECB:
        req->mode = SM4_MODE_ECB;
        break;
    case GMALG_SM1_CBC:
    case GMALG_SM4_CBC:
        req->mode = SM4_MODE_CBC;
        break;
    }

    /* 组装IV和数据 */
    memcpy(req->data, pucIV ? pucIV : null_iv, 16);
    memcpy(req->data + 16, pucEncData, uiEncDataLength);

    /* 设置密钥 */
    if (set_alg_key(handle->sm4_fd, pucKey, 16) != 0) {
        gmalg_debug("设置SM4密钥失败");
        free(req);
        return -1;
    }

    /* 发送解密请求 */
    ret = send_alg_request(handle->sm4_fd, req, req_size, &resp_data, &resp_len);
    free(req);

    if (ret != 0) {
        gmalg_debug("发送SM4解密请求失败");
        return -1;
    }

    resp = (struct sm_alg_resp *)resp_data;
    if (resp->result != 0) {
        gmalg_debug("SM4解密操作失败，错误码=%d", resp->result);
        free(resp_data);
        return -1;
    }

    /* 复制明文数据 */
    memcpy(pucData, resp->data, resp->output_len);
    if (puiDataLength) {
        *puiDataLength = resp->output_len;
    }

    free(resp_data);
    gmalg_debug("成功完成SM4解密，算法=0x%08x，密文%u字节", uiAlgID, uiEncDataLength);
    return 0;
}

/* ============ SM3哈希相关函数实现 ============ */

/*
 * 描述： 三步式数据杂凑运算第一步
 * 参数： hDeviceHandle[in]  与设备建立的会话句柄
 *        pucPublicKey[in]   签名者的ECC公钥，产生用于ECC签名的杂凑值时有效
 *        pucID[in]          签名者的ID值，产生用于ECC签名的杂凑值时有效
 *        uiIDLength[in]     签名者的ID长度
 * 返回值： 0 成功，非0 失败
 */
int GMALG_HashInit(void *hDeviceHandle, ECCrefPublicKey *pucPublicKey, 
                   unsigned char *pucID, unsigned int uiIDLength)
{
    const char *default_user_id = "1234567812345678";
    
    if (g_gmalg_mutex) g_gmalg_mutex->lock(g_gmalg_mutex);

    /* 重置哈希上下文 */
    g_hash_ctx.buffer_len = 0;
    g_hash_ctx.finalized = false;

    /* 如果提供了公钥和ID，先添加Z值计算相关的数据 */
    if (pucPublicKey && pucID && uiIDLength > 0) {
        /* 这里应该按照SM2标准计算Z值，简化实现中我们只记录用户ID */
        if (uiIDLength > 0 && g_hash_ctx.buffer_capacity >= uiIDLength) {
            memcpy(g_hash_ctx.buffer, pucID, uiIDLength);
            g_hash_ctx.buffer_len = uiIDLength;
        }
    } else {
        /* 使用默认用户ID */
        memcpy(g_hash_ctx.buffer, default_user_id, 16);
        g_hash_ctx.buffer_len = 16;
    }

    if (g_gmalg_mutex) g_gmalg_mutex->unlock(g_gmalg_mutex);
    gmalg_debug("SM3哈希初始化完成，ID长度=%u", uiIDLength);
    return 0;
}

/*
 * 描述： 三步式数据杂凑运算第二步，对输入的明文进行杂凑运算
 * 参数： hDeviceHandle[in]  与设备建立的会话句柄
 *        pucData[in]        缓冲区指针，用于存放输入的数据明文
 *        uiDataLength[in]   输入的数据明文长度
 * 返回值： 0 成功，非0 失败
 */
int GMALG_HashUpdate(void *hDeviceHandle, unsigned char *pucData, unsigned int uiDataLength)
{
    CHECK_POINTER(pucData);
    
    if (uiDataLength == 0) {
        return 0;  /* 允许长度为0的更新 */
    }
    
    if (g_gmalg_mutex) g_gmalg_mutex->lock(g_gmalg_mutex);

    if (g_hash_ctx.finalized) {
        gmalg_debug("哈希上下文已完成，无法继续更新");
        if (g_gmalg_mutex) g_gmalg_mutex->unlock(g_gmalg_mutex);
        return -1;
    }

    /* 检查缓冲区容量 */
    if (g_hash_ctx.buffer_len + uiDataLength > g_hash_ctx.buffer_capacity) {
        /* 扩展缓冲区 */
        size_t new_capacity = g_hash_ctx.buffer_capacity * 2;
        while (new_capacity < g_hash_ctx.buffer_len + uiDataLength) {
            new_capacity *= 2;
        }

        unsigned char *new_buffer = realloc(g_hash_ctx.buffer, new_capacity);
        if (!new_buffer) {
            gmalg_debug("扩展哈希缓冲区失败");
            if (g_gmalg_mutex) g_gmalg_mutex->unlock(g_gmalg_mutex);
            return -1;
        }

        g_hash_ctx.buffer = new_buffer;
        g_hash_ctx.buffer_capacity = new_capacity;
        gmalg_debug("哈希缓冲区扩展到%zu字节", new_capacity);
    }

    /* 追加数据到缓冲区 */
    memcpy(g_hash_ctx.buffer + g_hash_ctx.buffer_len, pucData, uiDataLength);
    g_hash_ctx.buffer_len += uiDataLength;

    if (g_gmalg_mutex) g_gmalg_mutex->unlock(g_gmalg_mutex);
    gmalg_debug("SM3哈希更新%u字节，总计%zu字节", uiDataLength, g_hash_ctx.buffer_len);
    return 0;
}


/*
 * 描述： 三步式数据杂凑运算第三步，杂凑运算结束返回杂凑数据
 * 参数： hDeviceHandle[in]  与设备建立的会话句柄
 *        pucHash[out]       缓冲区指针，用于存放输出的杂凑数据
 *        puiHashLength[out] 返回的杂凑数据长度
 * 返回值： 0 成功，非0 失败
 */
int GMALG_HashFinal(void *hDeviceHandle, unsigned char *pucHash, unsigned int *puiHashLength)
{
    gmalg_device_handle *handle = (gmalg_device_handle *)hDeviceHandle;
    int ret;
    
    CHECK_HANDLE(handle);
    CHECK_POINTER(pucHash);
    
    if (g_gmalg_mutex) g_gmalg_mutex->lock(g_gmalg_mutex);

    if (g_hash_ctx.finalized) {
        gmalg_debug("哈希上下文已完成");
        if (g_gmalg_mutex) g_gmalg_mutex->unlock(g_gmalg_mutex);
        return -1;
    }
    
    /* 计算最终哈希值 */
    ret = GMALG_SM3Hash(hDeviceHandle, g_hash_ctx.buffer, g_hash_ctx.buffer_len, pucHash);
    
    if (ret == 0) {
        g_hash_ctx.finalized = true;
        if (puiHashLength) {
            *puiHashLength = 32;  /* SM3输出固定32字节 */
        }
        gmalg_debug("SM3哈希计算完成，总数据%zu字节", g_hash_ctx.buffer_len);
    } else {
        gmalg_debug("SM3哈希计算失败");
    }
    
    if (g_gmalg_mutex) g_gmalg_mutex->unlock(g_gmalg_mutex);
    return ret;
}

/* 便捷的SM3哈希函数 */
int GMALG_SM3Hash(void *hDeviceHandle, unsigned char *pucData, unsigned int uiDataLength, 
                  unsigned char *pucHash)
{
    gmalg_device_handle *handle = (gmalg_device_handle *)hDeviceHandle;
    struct sm_alg_req *req;
    struct sm_alg_resp *resp;
    void *resp_data;
    size_t resp_len;
    size_t req_size;
    int ret;

    CHECK_HANDLE(handle);
    CHECK_POINTER(pucData);
    CHECK_POINTER(pucHash);

    if (uiDataLength == 0) {
        gmalg_debug("哈希数据长度为0");
        return -1;
    }

    /* 计算请求大小 */
    req_size = sizeof(struct sm_alg_req) + uiDataLength;
    req = malloc(req_size);
    if (!req) {
        gmalg_debug("分配请求内存失败");
        return -1;
    }

    /* 构造哈希请求 */
    req->operation = ALG_OP_SM3_HASH;
    req->key_len = 0;
    req->data_len = uiDataLength;
    req->output_len = 32;
    req->user_id_len = 0;
    req->mode = 0;

    /* 复制数据 */
    memcpy(req->data, pucData, uiDataLength);

    /* 发送哈希请求 */
    ret = send_alg_request(handle->sm3_fd, req, req_size, &resp_data, &resp_len);
    free(req);

    if (ret != 0) {
        gmalg_debug("发送SM3哈希请求失败");
        return -1;
    }

    resp = (struct sm_alg_resp *)resp_data;
    if (resp->result != 0) {
        gmalg_debug("SM3哈希操作失败，错误码=%d", resp->result);
        free(resp_data);
        return -1;
    }

    if (resp->output_len != 32) {
        gmalg_debug("SM3哈希长度错误，期望32字节，实际%u字节", resp->output_len);
        free(resp_data);
        return -1;
    }

    /* 复制哈希结果 */
    memcpy(pucHash, resp->data, 32);

    free(resp_data);
    gmalg_debug("成功完成SM3哈希，数据%u字节", uiDataLength);
    return 0;
}



/* ============ 密钥协商相关函数实现 ============ */

/*
 * 密钥协商句柄结构
 */
struct agreement_handle {
    ECCrefPrivateKey temp_private_key;
    ECCrefPublicKey temp_public_key;
    ECCrefPublicKey sponsor_public_key;
    unsigned char sponsor_id[256];
    unsigned int sponsor_id_len;
    unsigned int key_len;
};

/*
 * 描述： 使用 ECC 密钥协商算法，为计算会话密钥而产生协商参数
 * 参数： hDeviceHandle[in]        与设备建立的会话句柄
 *        pucSponsePrivateKey[in]  密码设备加密私钥
 *        pucSponsorPublicKey[in]  发起方公钥
 *        uiKey[in]                要求协商的密钥字节长度
 *        pucSponsorID[in]         参与密钥协商的发起方 ID 值
 *        uiSponsorIDLength[in]    发起方 ID 长度
 *        pucSponsorTmpPublicKey[out] 返回的发起方临时 ECC 公钥结构
 *        phAgreementHandle[out]   返回的协商句柄，用于计算协商密钥
 * 返回值： 0 成功，非0 失败
 */
int GMALG_GenerateAgreementDataWithECC(void *hDeviceHandle, ECCrefPrivateKey *pucSponsePrivateKey,
                                        ECCrefPublicKey *pucSponsorPublicKey, unsigned int uiKey,
                                        unsigned char *pucSponsorID, unsigned int uiSponsorIDLength,
                                        ECCrefPublicKey *pucSponsorTmpPublicKey, void **phAgreementHandle)
{
    struct agreement_handle *agreement;
    int ret;

    CHECK_POINTER(pucSponsePrivateKey);
    CHECK_POINTER(pucSponsorPublicKey);
    CHECK_POINTER(pucSponsorTmpPublicKey);
    CHECK_POINTER(phAgreementHandle);

    /* 分配协商句柄 */
    agreement = malloc(sizeof(struct agreement_handle));
    if (!agreement) {
        gmalg_debug("分配协商句柄内存失败");
        return -1;
    }

    /* 生成临时密钥对 */
    ret = GMALG_GenerateKeyPair_ECC(hDeviceHandle, &agreement->temp_public_key, &agreement->temp_private_key);
    if (ret != 0) {
        gmalg_debug("生成临时密钥对失败");
        free(agreement);
        return -1;
    }

    /* 保存协商参数 */
    memcpy(&agreement->sponsor_public_key, pucSponsorPublicKey, sizeof(ECCrefPublicKey));
    if (pucSponsorID && uiSponsorIDLength > 0) {
        memcpy(agreement->sponsor_id, pucSponsorID, min(uiSponsorIDLength, sizeof(agreement->sponsor_id)));
        agreement->sponsor_id_len = min(uiSponsorIDLength, sizeof(agreement->sponsor_id));
    } else {
        memcpy(agreement->sponsor_id, "1234567812345678", 16);
        agreement->sponsor_id_len = 16;
    }
    agreement->key_len = uiKey;

    /* 返回临时公钥 */
    memcpy(pucSponsorTmpPublicKey, &agreement->temp_public_key, sizeof(ECCrefPublicKey));

    *phAgreementHandle = agreement;
    gmalg_debug("成功生成密钥协商数据，协商密钥长度=%u", uiKey);
    return 0;
}

/*
 * 描述： 使用ECC密钥协商算法，使用协商句柄计算会话密钥
 * 参数： hDeviceHandle[in]           与设备建立的会话句柄
 *        pucResponseID[in]           外部输入的响应方 ID 值
 *        uiResponseIDLength[in]      外部输入的响应方 ID 长度
 *        pucResponsePublicKey[in]    外部输入的响应方 ECC 公钥结构
 *        pucResponseTmpPublicKey[in] 外部输入的响应方临时 ECC 公钥结构
 *        hAgreementHandle[in]        协商句柄，用于计算协商密钥
 *        phKey[out]                  返回的密钥数据
 * 返回值： 0 成功，非0 失败
 */
int GMALG_GenerateKeyWithECC(void *hDeviceHandle, unsigned char *pucResponseID, unsigned int uiResponseIDLength,
                              ECCrefPublicKey *pucResponsePublicKey, ECCrefPublicKey *pucResponseTmpPublicKey,
                              void *hAgreementHandle, void *phKey)
{
    struct agreement_handle *agreement = (struct agreement_handle *)hAgreementHandle;
    unsigned char combined_data[sizeof(ECCrefPublicKey) * 3 + 512];  /* 足够大的缓冲区 */
    size_t combined_len = 0;
    unsigned char shared_hash[32];
    int ret;

    CHECK_POINTER(agreement);
    CHECK_POINTER(pucResponsePublicKey);
    CHECK_POINTER(pucResponseTmpPublicKey);
    CHECK_POINTER(phKey);

    /* 组合协商数据：发起方公钥 + 响应方公钥 + 临时公钥 + ID信息 */
    memcpy(combined_data + combined_len, &agreement->sponsor_public_key, sizeof(ECCrefPublicKey));
    combined_len += sizeof(ECCrefPublicKey);

    memcpy(combined_data + combined_len, pucResponsePublicKey, sizeof(ECCrefPublicKey));
    combined_len += sizeof(ECCrefPublicKey);

    memcpy(combined_data + combined_len, pucResponseTmpPublicKey, sizeof(ECCrefPublicKey));
    combined_len += sizeof(ECCrefPublicKey);

    /* 添加ID信息 */
    memcpy(combined_data + combined_len, agreement->sponsor_id, agreement->sponsor_id_len);
    combined_len += agreement->sponsor_id_len;

    if (pucResponseID && uiResponseIDLength > 0) {
        memcpy(combined_data + combined_len, pucResponseID, uiResponseIDLength);
        combined_len += uiResponseIDLength;
    }

    /* 使用SM3计算共享密钥的基础哈希 */
    ret = GMALG_SM3Hash(hDeviceHandle, combined_data, combined_len, shared_hash);
    if (ret != 0) {
        gmalg_debug("计算协商哈希失败");
        free(agreement);
        return -1;
    }

    /* 使用KDF扩展密钥到所需长度 */
    if (agreement->key_len <= 32) {
        /* 直接使用哈希结果 */
        memcpy(phKey, shared_hash, agreement->key_len);
    } else {
        /* 需要扩展密钥长度，简化实现中重复使用哈希 */
        unsigned char *key_ptr = (unsigned char *)phKey;
        unsigned int remaining = agreement->key_len;
        unsigned int offset = 0;
        
        while (remaining > 0) {
            unsigned int copy_len = (remaining > 32) ? 32 : remaining;
            memcpy(key_ptr + offset, shared_hash, copy_len);
            offset += copy_len;
            remaining -= copy_len;
        }
    }

    /* 清除敏感数据 */
    secure_memzero(combined_data, sizeof(combined_data));
    secure_memzero(shared_hash, sizeof(shared_hash));
    free(agreement);

    gmalg_debug("成功生成协商密钥，长度=%u", agreement ? agreement->key_len : 0);
    return 0;
}

/*
 * 描述： 使用ECC密钥协商算法，产生协商参数并计算会话密钥
 * 参数： hDeviceHandle[in]            与设备建立的会话句柄
 *        pucResponsePrivateKey[in]    密码设备加密私钥
 *        pucResponsePublicKey[in]     密码设备加密公钥
 *        uiKey[in]                    协商后要求输出的密钥字节长度
 *        pucResponseID[in]            响应方 ID 值
 *        uiResponseIDLength[in]       响应方 ID 长度
 *        pucSponsorID[in]             发起方 ID 值
 *        uiSponsorIDLength[in]        发起方 ID 长度
 *        pucSponsorPublicKey[in]      外部输入的发起方 ECC 公钥结构
 *        pucSponsorTmpPublicKey[in]   外部输入的发起方临时 ECC 公钥结构
 *        pucResponseTmpPublicKey[out] 返回的响应方临时 ECC 公钥结构
 *        phKey[out]                   返回的密钥数据
 * 返回值： 0 成功，非0 失败
 */
int GMALG_GenerateAgreementDataAndKeyWithECC(void *hDeviceHandle, ECCrefPrivateKey *pucResponsePrivateKey,
                                              ECCrefPublicKey *pucResponsePublicKey, unsigned int uiKey,
                                              unsigned char *pucResponseID, unsigned int uiResponseIDLength,
                                              unsigned char *pucSponsorID, unsigned int uiSponsorIDLength,
                                              ECCrefPublicKey *pucSponsorPublicKey, ECCrefPublicKey *pucSponsorTmpPublicKey,
                                              ECCrefPublicKey *pucResponseTmpPublicKey, void *phKey)
{
    ECCrefPrivateKey tempPrivKey;
    unsigned char combined_data[sizeof(ECCrefPublicKey) * 4 + 512];
    size_t combined_len = 0;
    unsigned char shared_hash[32];
    int ret;

    CHECK_POINTER(pucResponsePrivateKey);
    CHECK_POINTER(pucResponsePublicKey);
    CHECK_POINTER(pucSponsorPublicKey);
    CHECK_POINTER(pucSponsorTmpPublicKey);
    CHECK_POINTER(pucResponseTmpPublicKey);
    CHECK_POINTER(phKey);

    /* 生成响应方临时密钥对 */
    ret = GMALG_GenerateKeyPair_ECC(hDeviceHandle, pucResponseTmpPublicKey, &tempPrivKey);
    if (ret != 0) {
        gmalg_debug("生成响应方临时密钥对失败");
        return ret;
    }

    /* 组合所有协商数据进行哈希 */
    memcpy(combined_data + combined_len, pucSponsorPublicKey, sizeof(ECCrefPublicKey));
    combined_len += sizeof(ECCrefPublicKey);

    memcpy(combined_data + combined_len, pucSponsorTmpPublicKey, sizeof(ECCrefPublicKey));
    combined_len += sizeof(ECCrefPublicKey);

    memcpy(combined_data + combined_len, pucResponsePublicKey, sizeof(ECCrefPublicKey));
    combined_len += sizeof(ECCrefPublicKey);

    memcpy(combined_data + combined_len, pucResponseTmpPublicKey, sizeof(ECCrefPublicKey));
    combined_len += sizeof(ECCrefPublicKey);

    /* 添加ID信息 */
    if (pucSponsorID && uiSponsorIDLength > 0) {
        memcpy(combined_data + combined_len, pucSponsorID, uiSponsorIDLength);
        combined_len += uiSponsorIDLength;
    }

    if (pucResponseID && uiResponseIDLength > 0) {
        memcpy(combined_data + combined_len, pucResponseID, uiResponseIDLength);
        combined_len += uiResponseIDLength;
    }

    /* 使用SM3计算共享密钥 */
    ret = GMALG_SM3Hash(hDeviceHandle, combined_data, combined_len, shared_hash);
    if (ret != 0) {
        gmalg_debug("计算协商哈希失败");
        /* 清除临时私钥 */
        secure_memzero(&tempPrivKey, sizeof(tempPrivKey));
        return ret;
    }

    /* 根据需要的密钥长度生成最终密钥 */
    if (uiKey <= 32) {
        memcpy(phKey, shared_hash, uiKey);
    } else {
        /* 扩展密钥长度 */
        unsigned char *key_ptr = (unsigned char *)phKey;
        unsigned int remaining = uiKey;
        unsigned int offset = 0;
        
        while (remaining > 0) {
            unsigned int copy_len = (remaining > 32) ? 32 : remaining;
            memcpy(key_ptr + offset, shared_hash, copy_len);
            offset += copy_len;
            remaining -= copy_len;
        }
    }

    /* 清除敏感数据 */
    secure_memzero(combined_data, sizeof(combined_data));
    secure_memzero(shared_hash, sizeof(shared_hash));
    secure_memzero(&tempPrivKey, sizeof(tempPrivKey));

    gmalg_debug("成功完成密钥协商，密钥长度=%u", uiKey);
    return 0;
}

/*
 * strongSwan GMALG AF_ALG接口完整实现
 * 通过Linux内核AF_ALG接口调用国密算法
 * 完全替换原有的用户空间实现，提供高性能的内核算法支持
 */
