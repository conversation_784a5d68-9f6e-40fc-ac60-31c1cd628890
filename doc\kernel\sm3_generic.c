// SPDX-License-Identifier: GPL-2.0-only
/*
 * SM3安全哈希算法实现
 * 基于OSCCA GM/T 0004-2012 SM3标准
 * 增强版本，同时支持原厂家SDK和strongSwan需求
 *
 * Copyright (C) 2017 ARM Limited or its affiliates.
 * Written by <PERSON> <<EMAIL>>
 * Enhanced for strongSwan support
 */

#include <crypto/internal/hash.h>
#include <linux/init.h>
#include <linux/module.h>
#include <linux/mm.h>
#include <linux/types.h>
#include <crypto/sm3.h>
#include <crypto/sm3_base.h>
#include <linux/bitops.h>
#include <asm/byteorder.h>
#include <asm/unaligned.h>

///* strongSwan兼容性增强结构 */
//struct sm3_context {
//	struct sm3_state state;
//	bool finalized;
//};

/* 零消息的SM3哈希值（空消息的哈希结果） */
const u8 sm3_zero_message_hash[SM3_DIGEST_SIZE] = {
	0x1A, 0xB2, 0x1D, 0x83, 0x55, 0xCF, 0xA1, 0x7F,
	0x8e, 0x61, 0x19, 0x48, 0x31, 0xE8, 0x1A, 0x8F,
	0x22, 0xBE, 0xC8, 0xC7, 0x28, 0xFE, 0xFB, 0x74,
	0x7E, 0xD0, 0x35, 0xEB, 0x50, 0x82, 0xAA, 0x2B
};
EXPORT_SYMBOL_GPL(sm3_zero_message_hash);

/* 置换函数P0 */
static inline u32 p0(u32 x)
{
	return x ^ rol32(x, 9) ^ rol32(x, 17);
}

/* 置换函数P1 */
static inline u32 p1(u32 x)
{
	return x ^ rol32(x, 15) ^ rol32(x, 23);
}

/* 布尔函数FF */
static inline u32 ff(unsigned int n, u32 a, u32 b, u32 c)
{
	return (n < 16) ? (a ^ b ^ c) : ((a & b) | (a & c) | (b & c));
}

/* 布尔函数GG */
static inline u32 gg(unsigned int n, u32 e, u32 f, u32 g)
{
	return (n < 16) ? (e ^ f ^ g) : ((e & f) | ((~e) & g));
}

/* 常数函数T */
static inline u32 t(unsigned int n)
{
	return (n < 16) ? SM3_T1 : SM3_T2;
}

/* 消息扩展函数 */
static void sm3_expand(u32 *t, u32 *w, u32 *wt)
{
	int i;
	unsigned int tmp;

	/* 加载输入消息 */
	for (i = 0; i <= 15; i++)
		w[i] = get_unaligned_be32((__u32 *)t + i);

	/* 消息扩展：生成W16到W67 */
	for (i = 16; i <= 67; i++) {
		tmp = w[i - 16] ^ w[i - 9] ^ rol32(w[i - 3], 15);
		w[i] = p1(tmp) ^ (rol32(w[i - 13], 7)) ^ w[i - 6];
	}

	/* 生成W'0到W'63 */
	for (i = 0; i <= 63; i++)
		wt[i] = w[i] ^ w[i + 4];
}

/* 压缩函数 */
static void sm3_compress(u32 *w, u32 *wt, u32 *m)
{
	u32 ss1;
	u32 ss2;
	u32 tt1;
	u32 tt2;
	u32 a, b, c, d, e, f, g, h;
	int i;

	/* 初始化工作变量 */
	a = m[0];
	b = m[1];
	c = m[2];
	d = m[3];
	e = m[4];
	f = m[5];
	g = m[6];
	h = m[7];

	/* 64轮迭代 */
	for (i = 0; i <= 63; i++) {
		/* 计算SS1 */
		ss1 = rol32((rol32(a, 12) + e + rol32(t(i), i & 31)), 7);

		/* 计算SS2 */
		ss2 = ss1 ^ rol32(a, 12);

		/* 计算TT1和TT2 */
		tt1 = ff(i, a, b, c) + d + ss2 + *wt;
		wt++;

		tt2 = gg(i, e, f, g) + h + ss1 + *w;
		w++;

		/* 更新工作变量 */
		d = c;
		c = rol32(b, 9);
		b = a;
		a = tt1;
		h = g;
		g = rol32(f, 19);
		f = e;
		e = p0(tt2);
	}

	/* 输出反馈 */
	m[0] = a ^ m[0];
	m[1] = b ^ m[1];
	m[2] = c ^ m[2];
	m[3] = d ^ m[3];
	m[4] = e ^ m[4];
	m[5] = f ^ m[5];
	m[6] = g ^ m[6];
	m[7] = h ^ m[7];

	/* 清除敏感数据 */
	a = b = c = d = e = f = g = h = ss1 = ss2 = tt1 = tt2 = 0;
}

/* SM3变换函数（处理单个消息块） */
static void sm3_transform(struct sm3_state *sst, u8 const *src)
{
	unsigned int w[68];
	unsigned int wt[64];

	/* 消息扩展和压缩 */
	sm3_expand((u32 *)src, w, wt);
	sm3_compress(w, wt, sst->state);

	/* 清除敏感数据 */
	memzero_explicit(w, sizeof(w));
	memzero_explicit(wt, sizeof(wt));
}

/* SM3通用块处理函数 */
static void sm3_generic_block_fn(struct sm3_state *sst, u8 const *src,
				    int blocks)
{
	while (blocks--) {
		sm3_transform(sst, src);
		src += SM3_BLOCK_SIZE;
	}
}

/* SM3更新函数（添加数据到哈希计算中） */
int crypto_sm3_update(struct shash_desc *desc, const u8 *data,
			  unsigned int len)
{
	return sm3_base_do_update(desc, data, len, sm3_generic_block_fn);
}
EXPORT_SYMBOL(crypto_sm3_update);

/* SM3最终化函数（完成哈希计算） */
int crypto_sm3_final(struct shash_desc *desc, u8 *out)
{
	sm3_base_do_finalize(desc, sm3_generic_block_fn);
	return sm3_base_finish(desc, out);
}
EXPORT_SYMBOL(crypto_sm3_final);

/* SM3完成函数（更新并最终化） */
int crypto_sm3_finup(struct shash_desc *desc, const u8 *data,
			unsigned int len, u8 *hash)
{
	sm3_base_do_update(desc, data, len, sm3_generic_block_fn);
	return crypto_sm3_final(desc, hash);
}
EXPORT_SYMBOL(crypto_sm3_finup);

/* strongSwan兼容的SM3上下文初始化函数 */
int sm3_init_ctx(struct sm3_context *ctx)
{
	if (!ctx)
		return -EINVAL;

	/* 初始化SM3状态 */
	ctx->state.state[0] = SM3_IVA;
	ctx->state.state[1] = SM3_IVB;
	ctx->state.state[2] = SM3_IVC;
	ctx->state.state[3] = SM3_IVD;
	ctx->state.state[4] = SM3_IVE;
	ctx->state.state[5] = SM3_IVF;
	ctx->state.state[6] = SM3_IVG;
	ctx->state.state[7] = SM3_IVH;
	ctx->state.count = 0;
	ctx->finalized = false;

	return 0;
}
EXPORT_SYMBOL(sm3_init_ctx);

/* strongSwan兼容的SM3数据更新函数 */
int sm3_update_ctx(struct sm3_context *ctx, const u8 *data, unsigned int len)
{
	unsigned int partial;
	int blocks;

	if (!ctx || ctx->finalized)
		return -EINVAL;

	if (!data && len > 0)
		return -EINVAL;

	if (len == 0)
		return 0;

	partial = ctx->state.count % SM3_BLOCK_SIZE;
	ctx->state.count += len;

	/* 处理部分块 */
	if (unlikely((partial + len) >= SM3_BLOCK_SIZE)) {
		if (partial) {
			int p = SM3_BLOCK_SIZE - partial;

			memcpy(ctx->state.buffer + partial, data, p);
			data += p;
			len -= p;

			sm3_generic_block_fn(&ctx->state, ctx->state.buffer, 1);
		}

		/* 处理完整块 */
		blocks = len / SM3_BLOCK_SIZE;
		len %= SM3_BLOCK_SIZE;

		if (blocks) {
			sm3_generic_block_fn(&ctx->state, data, blocks);
			data += blocks * SM3_BLOCK_SIZE;
		}
		partial = 0;
	}

	/* 保存剩余数据 */
	if (len)
		memcpy(ctx->state.buffer + partial, data, len);

	return 0;
}
EXPORT_SYMBOL(sm3_update_ctx);

/* strongSwan兼容的SM3最终化函数 */
int sm3_final_ctx(struct sm3_context *ctx, u8 *out)
{
	const int bit_offset = SM3_BLOCK_SIZE - sizeof(__be64);
	__be64 *bits;
	unsigned int partial;
	__be32 *digest;
	int i;

	if (!ctx || !out || ctx->finalized)
		return -EINVAL;

	bits = (__be64 *)(ctx->state.buffer + bit_offset);
	partial = ctx->state.count % SM3_BLOCK_SIZE;

	/* 添加填充 */
	ctx->state.buffer[partial++] = 0x80;
	if (partial > bit_offset) {
		memset(ctx->state.buffer + partial, 0x0, SM3_BLOCK_SIZE - partial);
		partial = 0;

		sm3_generic_block_fn(&ctx->state, ctx->state.buffer, 1);
	}

	/* 添加长度信息 */
	memset(ctx->state.buffer + partial, 0x0, bit_offset - partial);
	*bits = cpu_to_be64(ctx->state.count << 3);
	sm3_generic_block_fn(&ctx->state, ctx->state.buffer, 1);

	/* 输出最终哈希值 */
	digest = (__be32 *)out;
	for (i = 0; i < SM3_DIGEST_SIZE / sizeof(__be32); i++)
		put_unaligned_be32(ctx->state.state[i], digest++);

	/* 标记为已完成 */
	ctx->finalized = true;

	/* 清除敏感数据 */
	memzero_explicit(&ctx->state, sizeof(ctx->state));

	return 0;
}
EXPORT_SYMBOL(sm3_final_ctx);

/* strongSwan兼容的一次性SM3哈希函数 */
int sm3_hash(const u8 *data, unsigned int len, u8 *out)
{
	struct sm3_context ctx;
	int ret;

	ret = sm3_init_ctx(&ctx);
	if (ret)
		return ret;

	ret = sm3_update_ctx(&ctx, data, len);
	if (ret)
		return ret;

	return sm3_final_ctx(&ctx, out);
}
EXPORT_SYMBOL(sm3_hash);

/* strongSwan兼容的SM3 HMAC函数 */
int sm3_hmac(const u8 *key, unsigned int key_len,
	     const u8 *data, unsigned int data_len, u8 *out)
{
	u8 k_ipad[SM3_BLOCK_SIZE];
	u8 k_opad[SM3_BLOCK_SIZE];
	u8 key_hash[SM3_DIGEST_SIZE];
	u8 inner_hash[SM3_DIGEST_SIZE];
	struct sm3_context ctx;
	int i, ret;

	/* 如果密钥长度大于块大小，先哈希密钥 */
	if (key_len > SM3_BLOCK_SIZE) {
		ret = sm3_hash(key, key_len, key_hash);
		if (ret)
			return ret;
		key = key_hash;
		key_len = SM3_DIGEST_SIZE;
	}

	/* 准备内部和外部填充 */
	memset(k_ipad, 0x36, SM3_BLOCK_SIZE);
	memset(k_opad, 0x5c, SM3_BLOCK_SIZE);

	for (i = 0; i < key_len; i++) {
		k_ipad[i] ^= key[i];
		k_opad[i] ^= key[i];
	}

	/* 计算内部哈希：H(K ⊕ ipad || text) */
	ret = sm3_init_ctx(&ctx);
	if (ret)
		goto cleanup;

	ret = sm3_update_ctx(&ctx, k_ipad, SM3_BLOCK_SIZE);
	if (ret)
		goto cleanup;

	ret = sm3_update_ctx(&ctx, data, data_len);
	if (ret)
		goto cleanup;

	ret = sm3_final_ctx(&ctx, inner_hash);
	if (ret)
		goto cleanup;

	/* 计算外部哈希：H(K ⊕ opad || inner_hash) */
	ret = sm3_init_ctx(&ctx);
	if (ret)
		goto cleanup;

	ret = sm3_update_ctx(&ctx, k_opad, SM3_BLOCK_SIZE);
	if (ret)
		goto cleanup;

	ret = sm3_update_ctx(&ctx, inner_hash, SM3_DIGEST_SIZE);
	if (ret)
		goto cleanup;

	ret = sm3_final_ctx(&ctx, out);

cleanup:
	/* 清除敏感数据 */
	memzero_explicit(k_ipad, sizeof(k_ipad));
	memzero_explicit(k_opad, sizeof(k_opad));
	memzero_explicit(key_hash, sizeof(key_hash));
	memzero_explicit(inner_hash, sizeof(inner_hash));

	return ret;
}
EXPORT_SYMBOL(sm3_hmac);

/* SM3密钥派生函数（KDF） */
int sm3_kdf(const u8 *input, unsigned int input_len, 
	    u8 *output, unsigned int output_len)
{
	struct sm3_context ctx;
	u8 counter_bytes[4];
	u32 counter = 1;
	unsigned int remaining = output_len;
	unsigned int chunk_len;
	int ret;

	if (!input || !output || output_len == 0)
		return -EINVAL;

	while (remaining > 0) {
		/* 计算当前轮次的输出长度 */
		chunk_len = min(remaining, (unsigned int)SM3_DIGEST_SIZE);

		/* 准备计数器（大端序） */
		counter_bytes[0] = (counter >> 24) & 0xff;
		counter_bytes[1] = (counter >> 16) & 0xff;
		counter_bytes[2] = (counter >> 8) & 0xff;
		counter_bytes[3] = counter & 0xff;

		/* 计算 H(input || counter) */
		ret = sm3_init_ctx(&ctx);
		if (ret)
			return ret;

		ret = sm3_update_ctx(&ctx, input, input_len);
		if (ret)
			return ret;

		ret = sm3_update_ctx(&ctx, counter_bytes, 4);
		if (ret)
			return ret;

		if (chunk_len == SM3_DIGEST_SIZE) {
			/* 直接输出完整的哈希值 */
			ret = sm3_final_ctx(&ctx, output);
		} else {
			/* 输出部分哈希值 */
			u8 temp_hash[SM3_DIGEST_SIZE];
			ret = sm3_final_ctx(&ctx, temp_hash);
			if (ret == 0)
				memcpy(output, temp_hash, chunk_len);
			memzero_explicit(temp_hash, sizeof(temp_hash));
		}

		if (ret)
			return ret;

		output += chunk_len;
		remaining -= chunk_len;
		counter++;
	}

	return 0;
}
EXPORT_SYMBOL(sm3_kdf);

/* 原始SM3算法结构定义（保持兼容性） */
static struct shash_alg sm3_alg = {
	.digestsize	=	SM3_DIGEST_SIZE,
	.init		=	sm3_base_init,
	.update		=	crypto_sm3_update,
	.final		=	crypto_sm3_final,
	.finup		=	crypto_sm3_finup,
	.descsize	=	sizeof(struct sm3_state),
	.base		=	{
		.cra_name	 =	"sm3",
		.cra_driver_name =	"sm3-generic",
		.cra_blocksize	 =	SM3_BLOCK_SIZE,
		.cra_module	 =	THIS_MODULE,
	}
};

/* 模块初始化函数 */
static int __init sm3_generic_mod_init(void)
{
	return crypto_register_shash(&sm3_alg);
}

/* 模块退出函数 */
static void __exit sm3_generic_mod_fini(void)
{
	crypto_unregister_shash(&sm3_alg);
}

subsys_initcall(sm3_generic_mod_init);
module_exit(sm3_generic_mod_fini);

MODULE_LICENSE("GPL v2");
MODULE_DESCRIPTION("SM3安全哈希算法，支持strongSwan");

MODULE_ALIAS_CRYPTO("sm3");
MODULE_ALIAS_CRYPTO("sm3-generic");