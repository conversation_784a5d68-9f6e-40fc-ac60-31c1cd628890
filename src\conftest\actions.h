/*
 * Copyright (C) 2010 <PERSON>
 * Copyright (C) 2010 revosec AG
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License as published by the
 * Free Software Foundation; either version 2 of the License, or (at your
 * option) any later version.  See <http://www.fsf.org/copyleft/gpl.txt>.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
 * for more details.
 */

/**
 * @defgroup actions actions
 * @{ @ingroup conftest
 */

#ifndef ACTIONS_H_
#define ACTIONS_H_

typedef struct actions_t actions_t;

/**
 * actionss to trigger based on configuration.
 */
struct actions_t {

	/**
	 * Destroy a actions_t.
	 */
	void (*destroy)(actions_t *this);
};

/**
 * Create a actions instance.
 */
actions_t *actions_create();

#endif /** ACTIONS_H_ @}*/
