/* SPDX-License-Identifier: GPL-2.0-or-later */
/*
 * sm2.h - SM2非对称公钥算法头文件
 * 基于OSCCA GM/T 0003.1-2012 -- 0003.5-2012 SM2标准
 * 增强版本，同时支持原厂家SDK和strongSwan需求
 *
 * Written by <PERSON> <<EMAIL>>
 * Enhanced for strongSwan support
 */

#ifndef _CRYPTO_SM2_H
#define _CRYPTO_SM2_H

#include <crypto/sm3.h>
#include <crypto/akcipher.h>
#include <linux/mpi.h>

/* GM/T 0009-2012标准中指定的默认用户ID */
#define SM2_DEFAULT_USERID "1234567812345678"
#define SM2_DEFAULT_USERID_LEN 16

/* SM2密钥长度定义 */
#define SM2_KEY_SIZE 32
#define SM2_SIGNATURE_SIZE 64
#define SM2_MAX_PLAINTEXT_SIZE 255
#define SM2_MAX_CIPHERTEXT_SIZE (1 + 64 + 32 + SM2_MAX_PLAINTEXT_SIZE)

/* strongSwan兼容性结构声明 */
//struct sm2_key_pair;
//struct sm2_signature;
/* strongSwan兼容性结构定义 */
struct sm2_key_pair {
    MPI private_key;    /* 私钥 */
    MPI_POINT public_key;   /* 公钥点 */
};

struct sm2_signature {
    MPI r;  /* 签名r分量 */
    MPI s;  /* 签名s分量 */
};



/* 原始函数声明（保持兼容性） */
extern int sm2_compute_z_digest(struct crypto_akcipher *tfm,
			const unsigned char *id, size_t id_len,
			unsigned char dgst[SM3_DIGEST_SIZE]);

/* strongSwan兼容函数声明 */

/**
 * sm2_generate_keypair - 生成SM2密钥对
 * @ec: 椭圆曲线上下文
 * @keypair: 输出的密钥对
 * 
 * 返回: 成功时返回0，失败时返回负错误码
 */
extern int sm2_generate_keypair(struct mpi_ec_ctx *ec, struct sm2_key_pair *keypair);

/**
 * sm2_sign_data - 对数据进行SM2签名
 * @ec: 椭圆曲线上下文
 * @private_key: 私钥
 * @data: 待签名的数据
 * @data_len: 数据长度
 * @signature: 输出的签名
 * 
 * 返回: 成功时返回0，失败时返回负错误码
 */
//extern int sm2_sign_data(struct mpi_ec_ctx *ec, MPI private_key, 
//		         const u8 *data, size_t data_len,
//		         struct sm2_signature *signature);
extern int sm2_sign_data(struct mpi_ec_ctx *ec, MPI private_key, 
		  const unsigned char *data, size_t data_len,
		  const unsigned char *user_id, size_t user_id_len,
		  struct sm2_signature *signature);
/**
 * sm2_verify_signature - 验证SM2签名
 * @ec: 椭圆曲线上下文
 * @public_key: 公钥点
 * @data: 原始数据
 * @data_len: 数据长度
 * @signature: 待验证的签名
 * 
 * 返回: 验证成功时返回0，失败时返回负错误码
 */
extern int sm2_verify_signature(struct mpi_ec_ctx *ec, MPI_POINT public_key,
			 const unsigned char *data, size_t data_len,
			 const unsigned char *user_id, size_t user_id_len,
			 struct sm2_signature *signature);

/**
 * sm2_encrypt_data - SM2公钥加密
 * @ec: 椭圆曲线上下文
 * @public_key: 接收方公钥
 * @plaintext: 明文数据
 * @plaintext_len: 明文长度
 * @ciphertext: 密文输出缓冲区
 * @ciphertext_len: 密文长度（输入时为缓冲区大小，输出时为实际长度）
 * 
 * 返回: 成功时返回0，失败时返回负错误码
 */
extern int sm2_encrypt_data(struct mpi_ec_ctx *ec, MPI_POINT public_key,
		           const unsigned char *plaintext, size_t plaintext_len,
		           unsigned char *ciphertext, size_t *ciphertext_len);

/**
 * sm2_decrypt_data - SM2私钥解密
 * @ec: 椭圆曲线上下文
 * @private_key: 接收方私钥
 * @ciphertext: 密文数据
 * @ciphertext_len: 密文长度
 * @plaintext: 明文输出缓冲区
 * @plaintext_len: 明文长度（输入时为缓冲区大小，输出时为实际长度）
 * 
 * 返回: 成功时返回0，失败时返回负错误码
 */
extern int sm2_decrypt_data(struct mpi_ec_ctx *ec, MPI private_key,
		           const unsigned char *ciphertext, size_t ciphertext_len,
		           unsigned char *plaintext, size_t *plaintext_len);

/* 内部辅助函数声明 */
extern int sm2_get_signature_r(void *context, size_t hdrlen, unsigned char tag,
			       const void *value, size_t vlen);
extern int sm2_get_signature_s(void *context, size_t hdrlen, unsigned char tag,
			       const void *value, size_t vlen);
				   
extern void sm2_free_signature(struct sm2_signature *sig);
extern void sm2_free_keypair(struct sm2_key_pair *keypair);
extern int sm2_ec_ctx_init(struct mpi_ec_ctx *ec);

#endif /* _CRYPTO_SM2_H */