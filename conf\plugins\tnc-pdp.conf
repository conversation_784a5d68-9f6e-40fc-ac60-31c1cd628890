tnc-pdp {

    # Whether to load the plugin. Can also be an integer to increase the
    # priority of this plugin.
    load = yes

    # Name of the strongSwan PDP as contained in the AAA certificate.
    # server =

    # Timeout in seconds before closing incomplete connections.
    # timeout =

    pt_tls {

        # Enable PT-TLS protocol on the strongSwan PDP.
        # enable = yes

        # PT-TLS server port the strongSwan PDP is listening on.
        # port = 271

    }

    radius {

        # Enable RADIUS protocol on the strongSwan PDP.
        # enable = yes

        # EAP tunnel method to be used.
        # method = ttls

        # RADIUS server port the strongSwan PDP is listening on.
        # port = 1812

        # Shared RADIUS secret between strongSwan PDP and NAS. If set, make sure
        # to adjust the permissions of the config file accordingly.
        # secret =

    }

}

