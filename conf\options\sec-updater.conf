# Options for the sec-updater tool.
sec-updater {

    # Global IMV policy database URI. If it contains a password, make sure to
    # adjust the permissions of the config file accordingly.
    # database =

    # Plugins to load in sec-updater tool.
    # load =

    # strongTNC manage.py command used to import SWID tags.
    # tnc_manage_command = /var/www/tnc/manage.py

    swid_gen {

        # SWID generator command to be executed.
        # command = /usr/local/bin/swid_generator

        tag_creator {

            # Name of the tagCreator entity.
            # name = strongSwan Project

            # regid of the tagCreator entity.
            # regid = strongswan.org

        }

    }

    tmp {

        # Temporary storage for downloaded deb package file.
        # deb_file = /tmp/sec-updater.deb

        # Temporary storage for generated SWID tags.
        # tag_file = /tmp/sec-updater.tag

    }

}

