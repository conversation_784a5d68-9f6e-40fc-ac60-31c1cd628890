# Section to configure the load-tester plugin, see LOAD TESTS in
# strongswan.conf(5) for details.
load-tester {

    # Whether to keep dynamic addresses even after the associated SA got
    # terminated.
    # addrs_keep = no

    # Network prefix length to use when installing dynamic addresses. If set to
    # -1 the full address is used (i.e. 32 or 128).
    # addrs_prefix = 16

    # Directory to load (intermediate) CA certificates from.
    # ca_dir =

    # Seconds to start CHILD_SA rekeying after setup.
    # child_rekey = 600

    # URI to a CRL to include as certificate distribution point in generated
    # certificates.
    # crl =

    # Delay between initiations for each thread.
    # delay = 0

    # Delete an IKE_SA as soon as it has been established.
    # delete_after_established = no

    # Digest algorithm used when issuing certificates.
    # digest = sha1

    # DPD delay to use in load test.
    # dpd_delay = 0

    # Base port to be used for requests (each client uses a different port).
    # dynamic_port = 0

    # EAP secret to use in load test.
    # eap_password = default-pwd

    # Enable the load testing plugin. WARNING: Never enable this plugin on
    # productive systems. It provides preconfigured credentials and allows an
    # attacker to authenticate as any user.
    # enable = no

    # CHILD_SA proposal to use for load tests.
    # esp = aes128-sha1

    # Fake the kernel interface to allow load-testing against self.
    # fake_kernel = no

    # Seconds to start IKE_SA rekeying after setup.
    # ike_rekey = 0

    # Global limit of concurrently established SAs during load test.
    # init_limit = 0

    # Address to initiate from.
    # initiator = 0.0.0.0

    # Authentication method(s) the initiator uses.
    # initiator_auth = pubkey

    # Initiator ID used in load test.
    # initiator_id =

    # Initiator ID to match against as responder.
    # initiator_match =

    # Traffic selector on initiator side, as proposed by initiator.
    # initiator_tsi =

    # Traffic selector on responder side, as proposed by initiator.
    # initiator_tsr =

    # Number of concurrent initiator threads to use in load test.
    # initiators = 0

    # Path to the issuer certificate (if not configured a hard-coded default
    # value is used).
    # issuer_cert =

    # Path to private key that is used to issue certificates (if not configured
    # a hard-coded default value is used).
    # issuer_key =

    # Number of IKE_SAs to initiate by each initiator in load test.
    # iterations = 1

    # Whether to load the plugin. Can also be an integer to increase the
    # priority of this plugin.
    load = yes

    # IPsec mode to use, one of tunnel, transport, or beet.
    # mode = tunnel

    # Provide INTERNAL_IPV4_ADDRs from a named pool.
    # pool =

    # Preshared key to use in load test.
    # preshared_key = <default-psk>

    # IKE proposal to use in load test.
    # proposal = aes128-sha1-modp768

    # Request an INTERNAL_IPV4_ADDR and INTERNAL_IPV6_ADDR from the server.
    # request_virtual_ip = no

    # Address to initiation connections to.
    # responder = 127.0.0.1

    # Authentication method(s) the responder uses.
    # responder_auth = pubkey

    # Responder ID used in load test.
    # responder_id =

    # Traffic selector on initiator side, as narrowed by responder.
    # responder_tsi = initiator_tsi

    # Traffic selector on responder side, as narrowed by responder.
    # responder_tsr = initiator_tsr

    # Shutdown the daemon after all IKE_SAs have been established.
    # shutdown_when_complete = no

    # Socket provided by the load-tester plugin.
    # socket = unix://${piddir}/charon.ldt

    # IKE version to use (0 means use IKEv2 as initiator and accept any version
    # as responder).
    # version = 0

    # Section that contains key/value pairs with address pools (in CIDR
    # notation) to use for a specific network interface e.g. eth0 =
    # *********/16.
    addrs {

    }

}

