.TH @IPSEC_SCRIPT_UPPER@ 8 "2013-10-29" "@IPSEC_VERSION@" "strongSwan"
.
.SH NAME
.
@IPSEC_SCRIPT@ \- invoke IPsec utilities
.
.SH SYNOPSIS
.
.SY @IPSEC_SCRIPT@
.I command
.RI [ arguments ]
.RI [ options ]
.YS
.
.SH DESCRIPTION
.
The
.B @IPSEC_SCRIPT@
utility invokes any of several utilities involved in controlling and monitoring
the IPsec encryption/authentication system, running the specified \fIcommand\fP
with the specified \fIarguments\fP and \fIoptions\fP as if it had been invoked
directly. This largely eliminates possible name collisions with other software,
and also permits some centralized services.
.P
All the commands described in this manual page are built-in and are used to
control and monitor IPsec connections as well as the IKE daemon.
.P
For other commands
.I @IPSEC_SCRIPT@
supplies the invoked
.I command
with a suitable PATH environment variable,
and also provides the environment variables listed under
.IR ENVIRONMENT .
.
.SS CONTROL COMMANDS
.
.TP
.BI "start [" "starter options" ]
calls
.B "starter"
which in turn parses \fIipsec.conf\fR and starts the IKE daemon \fIcharon\fR.
.
.TP
.B "update"
sends a \fIHUP\fR signal to
.BR "starter"
which in turn determines any changes in \fIipsec.conf\fR
and updates the configuration on the running IKE daemon \fIcharon\fR.
.
.TP
.B "reload"
sends a \fIUSR1\fR signal to
.BR "starter"
which in turn reloads the whole configuration of the running IKE daemon
\fIcharon\fR based on the actual \fIipsec.conf\fR.
.
.TP
.B "restart"
is equivalent to
.B "stop"
followed by
.B "start"
after a guard of 2 seconds.
.
.TP
.B "stop"
terminates all IPsec connections and stops the IKE daemon \fIcharon\fR
by sending a \fITERM\fR signal to
.BR "starter".
.
.TP
.BI "up " name
tells the IKE daemon to start up connection \fIname\fP.
.
.TP
.BI "down " name
tells the IKE daemon to terminate connection \fIname\fP.
.
.TP
.BI "down " name{n}
terminates IKEv1 Quick Mode and IKEv2 CHILD SA instance \fIn\fP of
connection \fIname\fP.
.
.TP
.BI "down " name{*}
terminates all IKEv1 Quick Mode and  IKEv2 CHILD SA instances of connection
\fIname\fP.
.
.TP
.BI "down " name[n]
terminates IKE SA instance \fIn\fP of connection \fIname\fP.
.
.TP
.BI "down " name[*]
terminates all IKE SA instances of connection \fIname\fP.
.
.TP
.BI "down-srcip <" start "> [<" end ">]"
terminates all IKE SA instances with clients having virtual IPs in the range
.IR start - end .
.
.TP
.BI "route " name
tells the IKE daemon to insert an IPsec policy in the kernel
for connection \fIname\fP. The first payload packet matching the IPsec policy
will automatically trigger an IKE connection setup.
.
.TP
.BI "unroute " name
remove the IPsec policy in the kernel for connection \fIname\fP.
.
.TP
.BI "status [" name ]
returns concise status information either on connection
\fIname\fP or if the argument is lacking, on all connections.
.
.TP
.BI "statusall [" name ]
returns detailed status information either on connection
\fIname\fP or if the argument is lacking, on all connections.
.
.SS LIST COMMANDS
.
.TP
.BI "leases [<" poolname "> [<" address ">]]"
returns the status of all or the selected IP address pool (or even a single
virtual IP address).
.
.TP
.B "listalgs"
returns a list supported cryptographic algorithms usable for IKE, and their
corresponding plugin.
.
.TP
.BI "listpubkeys [" --utc ]
returns a list of RSA public keys that were either loaded in raw key format
or extracted from X.509 and|or OpenPGP certificates.
.
.TP
.BI "listcerts [" --utc ]
returns a list of X.509 and|or OpenPGP certificates that were either loaded
locally by the IKE daemon or received via the IKE protocol.
.
.TP
.BI "listcacerts [" --utc ]
returns a list of X.509 Certification Authority (CA) certificates that were
loaded locally by the IKE daemon from the \fI/etc/ipsec.d/cacerts/\fP
directory or received via the IKE protocol.
.
.TP
.BI "listaacerts [" --utc ]
returns a list of X.509 Authorization Authority (AA) certificates that were
loaded locally by the IKE daemon from the \fI/etc/ipsec.d/aacerts/\fP
directory.
.
.TP
.BI "listocspcerts [" --utc ]
returns a list of X.509 OCSP Signer certificates that were either loaded
locally by the IKE daemon from the \fI/etc/ipsec.d/ocspcerts/\fP
directory or were sent by an OCSP server.
.
.TP
.BI "listacerts [" --utc ]
returns a list of X.509 Attribute certificates that were loaded locally by
the IKE daemon from the \fI/etc/ipsec.d/acerts/\fP directory.
.
.TP
.BI "listgroups [" --utc ]
returns a list of groups that are used to define user authorization profiles.
.
.TP
.BI "listcainfos [" --utc ]
returns certification authority information (CRL distribution points, OCSP URIs,
LDAP servers) that were defined by
.BR ca
sections in \fIipsec.conf\fP.
.
.TP
.BI "listcrls [" --utc ]
returns a list of Certificate Revocation Lists (CRLs) that were either loaded
by the IKE daemon from the \fI/etc/ipsec.d/crls\fP directory or fetched from
an HTTP- or LDAP-based CRL distribution point.
.
.TP
.BI "listocsp [" --utc ]
returns revocation information fetched from OCSP servers.
.
.TP
.BI "listplugins"
returns a list of all loaded plugin features.
.
.TP
.BI "listcounters [" name ]
returns a list of global or connection specific IKE counter values
collected since daemon startup.
.
.TP
.BI "listall [" --utc ]
returns all information generated by the list commands above. Each list command
can be called with the
\fB\-\-utc\fP
option which displays all dates in UTC instead of local time.
.
.SS REREAD COMMANDS
.
.TP
.B "rereadsecrets"
flushes and rereads all secrets defined in \fIipsec.secrets\fP.
.
.TP
.B "rereadcacerts"
removes previously loaded CA certificates, reads all certificate files
contained in the \fI/etc/ipsec.d/cacerts\fP directory and adds them to the list
of Certification Authority (CA) certificates. This does not affect certificates
explicitly defined in a
.BR ipsec.conf (5)
ca section, which may be separately updated using the \fBupdate\fP command.
.
.TP
.B "rereadaacerts"
removes previously loaded AA certificates, reads all certificate files
contained in the \fI/etc/ipsec.d/aacerts\fP directory and adds them to the list
of Authorization Authority (AA) certificates.
.
.TP
.B "rereadocspcerts"
reads all certificate files contained in the \fI/etc/ipsec.d/ocspcerts/\fP
directory and adds them to the list of OCSP signer certificates.
.
.TP
.B "rereadacerts"
reads all certificate files contained in the  \fI/etc/ipsec.d/acerts/\fP
directory and adds them to the list of attribute certificates.
.
.TP
.B "rereadcrls"
reads  all Certificate  Revocation Lists (CRLs) contained in the
\fI/etc/ipsec.d/crls/\fP directory and adds them to the list of CRLs.
.
.TP
.B "rereadall"
executes all reread commands listed above.
.
.SS RESET COMMANDS
.
.TP
.BI "resetcounters [" name ]
resets global or connection specific counters.
.
.SS PURGE COMMANDS
.
.TP
.B "purgecerts"
purges all cached certificates.
.
.TP
.B "purgecrls"
purges all cached CRLs.
.
.TP
.B "purgeike"
purges IKE SAs that don't have a Quick Mode or CHILD SA.
.
.TP
.B "purgeocsp"
purges all cached OCSP information records.
.
.SS INFO COMMANDS
.
.TP
.B "\-\-help"
returns the usage information for the
.B @IPSEC_SCRIPT@
command.
.
.TP
.B "\-\-version"
returns the version in the form of
.B Linux strongSwan U<strongSwan userland version>/K<Linux kernel version>
if strongSwan uses the native NETKEY IPsec stack of the Linux kernel it is
running on.
.
.TP
.B "\-\-versioncode"
returns the version number in the form of
.B U<strongSwan userland version>/K<Linux kernel version>
if strongSwan uses the native NETKEY IPsec stack of the Linux kernel it is
running on.
.
.TP
.B "\-\-copyright"
returns the copyright information.
.
.TP
.B "\-\-directory"
returns the \fILIBEXECDIR\fP directory as defined by the configure options.
.
.TP
.B "\-\-confdir"
returns the \fISYSCONFDIR\fP directory as defined by the configure options.
.
.TP
.B "\-\-piddir"
returns the \fIPIDDIR\fP directory as defined by the configure options.
.
.SH FILES
.
@IPSEC_DIR@		utilities directory
.
.SH ENVIRONMENT
.
When calling other commands the
.B @IPSEC_SCRIPT@
command supplies the following environment variables.
.nf
.na

IPSEC_DIR               directory containing ipsec programs and utilities
IPSEC_BINDIR            directory containing \fBpki\fP command
IPSEC_SBINDIR           directory containing \fBipsec\fP command
IPSEC_CONFDIR           directory containing configuration files
IPSEC_PIDDIR            directory containing PID/socket files
IPSEC_SCRIPT            name of the ipsec script
IPSEC_NAME              name of ipsec distribution
IPSEC_VERSION           version number of ipsec userland and kernel
IPSEC_STARTER_PID       PID file for ipsec starter
IPSEC_CHARON_PID        PID file for IKE keying daemon
.ad
.fi
.
.SH SEE ALSO
.
.BR ipsec.conf (5),
.BR ipsec.secrets (5)
.
.SH HISTORY
Originally written for the FreeS/WAN project by Henry Spencer.
Updated and extended for the strongSwan project <http://www.strongswan.org> by
Tobias Brunner and Andreas Steffen.
