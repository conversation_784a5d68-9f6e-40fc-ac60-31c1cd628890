random {

    # Whether to load the plugin. Can also be an integer to increase the
    # priority of this plugin.
    load = yes

    # File to read random bytes from.
    # random = ${random_device}

    # If set to yes the RNG_STRONG class reads random bytes from the same source
    # as the RNG_TRUE class.
    # strong_equals_true = no

    # File to read pseudo random bytes from.
    # urandom = ${urandom_device}

}

