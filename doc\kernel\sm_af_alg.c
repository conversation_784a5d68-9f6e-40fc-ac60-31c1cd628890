/* SPDX-License-Identifier: GPL-2.0 */
/*
 * 国密算法AF_ALG接口实现
 * 为SM2/SM3/SM4算法提供用户空间访问接口
 * 支持strongSwan通过socket方式调用内核国密算法
 */

#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/init.h>
#include <linux/net.h>
#include <linux/socket.h>
#include <net/sock.h>
#include <crypto/if_alg.h>
#include <crypto/hash.h>
#include <crypto/skcipher.h>
#include <crypto/akcipher.h>
#include <crypto/sm2.h>
#include <crypto/sm3.h>
#include <crypto/sm4.h>
#include <crypto/sm_af_alg.h> 
#include <linux/scatterlist.h>
#include <linux/uio.h>

///* AF_ALG操作类型定义 */
//#define ALG_OP_SM2_KEYGEN    1   /* SM2密钥生成 */
//#define ALG_OP_SM2_SIGN      2   /* SM2签名 */
//#define ALG_OP_SM2_VERIFY    3   /* SM2验证 */
//#define ALG_OP_SM2_ENCRYPT   4   /* SM2加密 */
//#define ALG_OP_SM2_DECRYPT   5   /* SM2解密 */
//#define ALG_OP_SM3_HASH      6   /* SM3哈希 */
//#define ALG_OP_SM3_HMAC      7   /* SM3 HMAC */
//#define ALG_OP_SM4_ENCRYPT   8   /* SM4加密 */
//#define ALG_OP_SM4_DECRYPT   9   /* SM4解密 */

///* strongSwan兼容的数据结构 */
//struct sm_alg_req {
//    u32 operation;          /* 操作类型 */
//    u32 key_len;           /* 密钥长度 */
//    u32 data_len;          /* 数据长度 */
//    u32 output_len;        /* 输出长度 */
//    u32 user_id_len;       /* 用户ID长度（SM2专用） */
//    u8 data[];             /* 变长数据：key + data + user_id */
//};

//struct sm_alg_resp {
//    u32 result;            /* 操作结果码 */
//    u32 output_len;        /* 实际输出长度 */
//    u8 data[];             /* 输出数据 */
//};

///* SM2 AF_ALG私有上下文 */
//struct sm2_alg_ctx {
//    struct crypto_akcipher *tfm;
//    struct mpi_ec_ctx ec_ctx;
//    struct sm2_key_pair keypair;
//    bool key_set;
//};

///* SM3 AF_ALG私有上下文 */
//struct sm3_alg_ctx {
//    struct crypto_shash *tfm;
//    struct sm3_context ctx;
//    bool initialized;
//};

///* SM4 AF_ALG私有上下文 */
//struct sm4_alg_ctx {
//    struct crypto_skcipher *tfm;
//    struct crypto_sm4_ctx sm4_ctx;
//    u8 key[SM4_KEY_SIZE];
//    bool key_set;
//};

/* ============ 通用AF_ALG操作函数 ============ */

static int sm_alg_recvmsg(struct socket *sock, struct msghdr *msg, size_t ignored, int flags)
{
    /* 通用接收消息函数 */
    return 0;  /* 在sendmsg中已经处理了响应 */
}

static int sm_alg_setsockopt(struct socket *sock, int level, int optname,
                             sockptr_t optval, unsigned int optlen)
{
    /* 通用socket选项设置函数 */
    return 0;
}

static int sm_alg_getsockopt(struct socket *sock, int level, int optname,
                             char __user *optval, int __user *optlen)
{
    /* 通用socket选项获取函数 */
    return 0;
}

static int sm_alg_release(struct socket *sock)
{
    struct sock *sk = sock->sk;
    struct alg_sock *ask = alg_sk(sk);
    
    /* 释放私有数据 */
    if (ask->private) {
        kfree(ask->private);
        ask->private = NULL;
    }
    
    return 0;
}

/* ============ SM2 AF_ALG实现 ============ */

int sm2_alg_sendmsg(struct socket *sock, struct msghdr *msg, size_t size)
{
    struct sock *sk = sock->sk;
    struct alg_sock *ask = alg_sk(sk);
    struct sm2_alg_ctx *ctx = ask->private;
    struct sm_alg_req *req;
    struct sm_alg_resp *resp;
    u8 *req_data, *resp_data;
    struct iov_iter iter;
    int ret = -EINVAL;
    size_t resp_size;
    
    /* 变量声明移到函数开始处 */
    u8 *pubkey_buf, *x_buf, *y_buf, *data, *user_id, *sig_buf;
    unsigned int x_len, y_len, r_len, s_len;
    struct sm2_signature signature;
    u8 *pubkey, *signature_data, *plaintext;
    size_t plaintext_len, ciphertext_len;
    u8 *ciphertext;
    size_t ciphertext_len_in, plaintext_len_in;
    MPI_POINT public_key;
    MPI x, y;
    u8 *r_buf, *s_buf;

    if (size < sizeof(struct sm_alg_req))
        return -EINVAL;

    req_data = kmalloc(size, GFP_KERNEL);
    if (!req_data)
        return -ENOMEM;
    
    /* 修复缩进问题 */
    iov_iter_init(&iter, WRITE, msg->msg_iter.iov, msg->msg_iter.nr_segs, size);
    if (copy_from_iter(req_data, size, &iter) != size) {
        kfree(req_data);
        return -EFAULT;
    }

    req = (struct sm_alg_req *)req_data;

    /* 分配响应数据缓冲区 */
    resp_data = kmalloc(sizeof(struct sm_alg_resp) + 1024, GFP_KERNEL);
    if (!resp_data) {
        kfree(req_data);
        return -ENOMEM;
    }

    resp = (struct sm_alg_resp *)resp_data;
    resp->result = 0;
    resp->output_len = 0;

    switch (req->operation) {
    case ALG_OP_SM2_KEYGEN:
        /* 生成SM2密钥对 */
        ret = sm2_generate_keypair(&ctx->ec_ctx, &ctx->keypair);
        if (ret == 0) {
            /* 导出公钥（未压缩格式：04 + x + y） */
            pubkey_buf = resp->data;
            pubkey_buf[0] = 0x04;  /* 未压缩标志 */

            x_buf = mpi_get_buffer(ctx->keypair.public_key->x, &x_len, NULL);
            y_buf = mpi_get_buffer(ctx->keypair.public_key->y, &y_len, NULL);

            if (x_buf && y_buf) {
                /* 确保坐标长度为32字节 */
                memset(pubkey_buf + 1, 0, 32);
                memset(pubkey_buf + 33, 0, 32);

                if (x_len <= 32)
                    memcpy(pubkey_buf + 1 + 32 - x_len, x_buf, x_len);
                else
                    memcpy(pubkey_buf + 1, x_buf + x_len - 32, 32);

                if (y_len <= 32)
                    memcpy(pubkey_buf + 33 + 32 - y_len, y_buf, y_len);
                else
                    memcpy(pubkey_buf + 33, y_buf + y_len - 32, 32);

                resp->output_len = 65;  /* 1 + 32 + 32 */
                ctx->key_set = true;
            } else {
                ret = -ENOMEM;
            }

            kfree(x_buf);
            kfree(y_buf);
        }
        break;

    case ALG_OP_SM2_SIGN:
        if (!ctx->key_set) {
            ret = -ENOKEY;
            break;
        }

        /* 解析输入：data + user_id */
        data = req->data;
        user_id = req->data + req->data_len;
        memset(&signature, 0, sizeof(signature));

        ret = sm2_sign_data(&ctx->ec_ctx, ctx->keypair.private_key,
                           data, req->data_len, user_id, req->user_id_len,
                           &signature);

        if (ret == 0) {
            /* 导出签名（r + s，各32字节） */
            sig_buf = resp->data;

            r_buf = mpi_get_buffer(signature.r, &r_len, NULL);
            s_buf = mpi_get_buffer(signature.s, &s_len, NULL);

            if (r_buf && s_buf) {
                memset(sig_buf, 0, 64);

                if (r_len <= 32)
                    memcpy(sig_buf + 32 - r_len, r_buf, r_len);
                else
                    memcpy(sig_buf, r_buf + r_len - 32, 32);

                if (s_len <= 32)
                    memcpy(sig_buf + 32 + 32 - s_len, s_buf, s_len);
                else
                    memcpy(sig_buf + 32, s_buf + s_len - 32, 32);

                resp->output_len = 64;
            } else {
                ret = -ENOMEM;
            }

            kfree(r_buf);
            kfree(s_buf);
            sm2_free_signature(&signature);
        }
        break;

    case ALG_OP_SM2_VERIFY:
        /* 解析输入：pubkey(65) + signature(64) + data + user_id */
        pubkey = req->data;
        signature_data = req->data + 65;
        data = req->data + 65 + 64;
        user_id = data + req->data_len;

        /* 解析公钥点 */
        public_key = mpi_point_new(0);
        x = mpi_new(256);
        y = mpi_new(256);

        if (public_key && x && y && pubkey[0] == 0x04) {
            mpi_read_buffer(x, pubkey + 1, 32, NULL, NULL);
            mpi_read_buffer(y, pubkey + 33, 32, NULL, NULL);
            mpi_set(public_key->x, x);
            mpi_set(public_key->y, y);
            mpi_set_ui(public_key->z, 1);

            /* 解析签名 */
            memset(&signature, 0, sizeof(signature));
            signature.r = mpi_new(256);
            signature.s = mpi_new(256);

            if (signature.r && signature.s) {
                mpi_read_buffer(signature.r, signature_data, 32, NULL, NULL);
                mpi_read_buffer(signature.s, signature_data + 32, 32, NULL, NULL);

                ret = sm2_verify_signature(&ctx->ec_ctx, public_key,
                                         data, req->data_len,
                                         user_id, req->user_id_len,
                                         &signature);

                sm2_free_signature(&signature);
            }
        }

        mpi_free(x);
        mpi_free(y);
        mpi_point_release(public_key);
        break;

    case ALG_OP_SM2_ENCRYPT:
        /* 解析输入：pubkey(65) + plaintext */
        pubkey = req->data;
        plaintext = req->data + 65;
        plaintext_len = req->data_len;
        ciphertext_len = 1024;  /* 预分配足够空间 */

        /* 解析公钥点 */
        public_key = mpi_point_new(0);
        x = mpi_new(256);
        y = mpi_new(256);

        if (public_key && x && y && pubkey[0] == 0x04) {
            mpi_read_buffer(x, pubkey + 1, 32, NULL, NULL);
            mpi_read_buffer(y, pubkey + 33, 32, NULL, NULL);
            mpi_set(public_key->x, x);
            mpi_set(public_key->y, y);
            mpi_set_ui(public_key->z, 1);

            ret = sm2_encrypt_data(&ctx->ec_ctx, public_key,
                                  plaintext, plaintext_len,
                                  resp->data, &ciphertext_len);

            if (ret == 0)
                resp->output_len = ciphertext_len;
        }

        mpi_free(x);
        mpi_free(y);
        mpi_point_release(public_key);
        break;

    case ALG_OP_SM2_DECRYPT:
        if (!ctx->key_set) {
            ret = -ENOKEY;
            break;
        }

        /* 解析输入：ciphertext */
        ciphertext = req->data;
        ciphertext_len_in = req->data_len;
        plaintext_len_in = 1024;  /* 预分配足够空间 */

        ret = sm2_decrypt_data(&ctx->ec_ctx, ctx->keypair.private_key,
                              ciphertext, ciphertext_len_in,
                              resp->data, &plaintext_len_in);

        if (ret == 0)
            resp->output_len = plaintext_len_in;
        break;

    default:
        ret = -EOPNOTSUPP;
        break;
    }

    resp->result = ret;
    resp_size = sizeof(struct sm_alg_resp) + resp->output_len;

    /* 将响应数据复制回用户空间 */
    if (copy_to_iter(resp_data, resp_size, &msg->msg_iter) != resp_size) {
        ret = -EFAULT;
    } else {
        ret = resp_size;
    }

    kfree(req_data);
    kfree(resp_data);
    return ret;
}

/* ============ SM3 AF_ALG实现 ============ */

int sm3_alg_init(struct sock *sk)
{
    struct alg_sock *ask = alg_sk(sk);
    struct sm3_alg_ctx *ctx;

    ctx = kzalloc(sizeof(*ctx), GFP_KERNEL);
    if (!ctx)
        return -ENOMEM;

    ctx->tfm = crypto_alloc_shash("sm3", 0, 0);
    if (IS_ERR(ctx->tfm)) {
        kfree(ctx);
        return PTR_ERR(ctx->tfm);
    }

    sm3_init_ctx(&ctx->ctx);
    ctx->initialized = true;
    ask->private = ctx;

    return 0;
}

int sm3_alg_sendmsg(struct socket *sock, struct msghdr *msg, size_t size)
{
    struct sm_alg_req *req;
    struct sm_alg_resp *resp;
    u8 *req_data, *resp_data;
    struct iov_iter iter;
    int ret = -EINVAL;
    size_t resp_size;

    if (size < sizeof(struct sm_alg_req))
        return -EINVAL;

    req_data = kmalloc(size, GFP_KERNEL);
    if (!req_data)
        return -ENOMEM;

    /* 修复缩进问题 */
    iov_iter_init(&iter, WRITE, msg->msg_iter.iov, msg->msg_iter.nr_segs, size);
    if (copy_from_iter(req_data, size, &iter) != size) {
        kfree(req_data);
        return -EFAULT;
    }

    req = (struct sm_alg_req *)req_data;

    resp_data = kmalloc(sizeof(struct sm_alg_resp) + 64, GFP_KERNEL);
    if (!resp_data) {
        kfree(req_data);
        return -ENOMEM;
    }

    resp = (struct sm_alg_resp *)resp_data;
    resp->result = 0;
    resp->output_len = 0;

    switch (req->operation) {
    case ALG_OP_SM3_HASH: {
        /* SM3哈希计算 */
        ret = sm3_hash(req->data, req->data_len, resp->data);
        if (ret == 0)
            resp->output_len = SM3_DIGEST_SIZE;
        break;
    }

    case ALG_OP_SM3_HMAC: {
        /* SM3 HMAC计算 */
        u8 *key = req->data;
        u8 *data = req->data + req->key_len;

        ret = sm3_hmac(key, req->key_len, data, req->data_len, resp->data);
        if (ret == 0)
            resp->output_len = SM3_DIGEST_SIZE;
        break;
    }

    default:
        ret = -EOPNOTSUPP;
        break;
    }

    resp->result = ret;
    resp_size = sizeof(struct sm_alg_resp) + resp->output_len;

    /* 修复变量名错误 */
    if (copy_to_iter(resp_data, resp_size, &msg->msg_iter) != resp_size) {
        ret = -EFAULT;
    } else {
        ret = resp_size;
    }

    kfree(req_data);
    kfree(resp_data);
    return ret;
}

/* ============ SM4 AF_ALG实现 ============ */

int sm4_alg_sendmsg(struct socket *sock, struct msghdr *msg, size_t size)
{
    struct sock *sk = sock->sk;
    struct alg_sock *ask = alg_sk(sk);
    struct sm4_alg_ctx *ctx = ask->private;
    struct sm_alg_req *req;
    struct sm_alg_resp *resp;
    u8 *req_data, *resp_data;
    struct iov_iter iter;
    int ret = -EINVAL;
    size_t resp_size;

    if (!ctx->key_set)
        return -ENOKEY;

    if (size < sizeof(struct sm_alg_req))
        return -EINVAL;

    req_data = kmalloc(size, GFP_KERNEL);
    if (!req_data)
        return -ENOMEM;

    /* 修复缩进问题 */
    iov_iter_init(&iter, WRITE, msg->msg_iter.iov, msg->msg_iter.nr_segs, size);
    if (copy_from_iter(req_data, size, &iter) != size) {
        kfree(req_data);
        return -EFAULT;
    }

    req = (struct sm_alg_req *)req_data;

    resp_data = kmalloc(sizeof(struct sm_alg_resp) + req->data_len + 16, GFP_KERNEL);
    if (!resp_data) {
        kfree(req_data);
        return -ENOMEM;
    }

    resp = (struct sm_alg_resp *)resp_data;
    resp->result = 0;
    resp->output_len = 0;

    switch (req->operation) {
    case ALG_OP_SM4_ENCRYPT: {
        /* 解析输入：iv + plaintext */
        u8 *iv = req->data;
        u8 *plaintext = req->data + 16;
        size_t plaintext_len = req->data_len;

        ret = sm4_cbc_encrypt(ctx->key, iv, plaintext, resp->data, plaintext_len);
        if (ret == 0)
            resp->output_len = plaintext_len;
        break;
    }

    case ALG_OP_SM4_DECRYPT: {
        /* 解析输入：iv + ciphertext */
        u8 *iv = req->data;
        u8 *ciphertext = req->data + 16;
        size_t ciphertext_len = req->data_len;

        ret = sm4_cbc_decrypt(ctx->key, iv, ciphertext, resp->data, ciphertext_len);
        if (ret == 0)
            resp->output_len = ciphertext_len;
        break;
    }

    default:
        ret = -EOPNOTSUPP;
        break;
    }

    resp->result = ret;
    resp_size = sizeof(struct sm_alg_resp) + resp->output_len;

    /* 修复变量名错误 */
    if (copy_to_iter(resp_data, resp_size, &msg->msg_iter) != resp_size) {
        ret = -EFAULT;
    } else {
        ret = resp_size;
    }

    kfree(req_data);
    kfree(resp_data);
    return ret;
}

/* ============ AF_ALG操作结构定义 ============ */

static struct proto_ops sm2_alg_ops = {
    .family     = PF_ALG,
    .owner      = THIS_MODULE,
    .release    = sm_alg_release,
    .sendmsg    = sm2_alg_sendmsg,
    .recvmsg    = sm_alg_recvmsg,
};

static struct proto_ops sm3_alg_ops = {
    .family     = PF_ALG,
    .owner      = THIS_MODULE,
    .release    = sm_alg_release,
    .sendmsg    = sm3_alg_sendmsg,
    .recvmsg    = sm_alg_recvmsg,
};

static struct proto_ops sm4_alg_ops = {
    .family     = PF_ALG,
    .owner      = THIS_MODULE,
    .release    = sm_alg_release,
    .sendmsg    = sm4_alg_sendmsg,
    .recvmsg    = sm_alg_recvmsg,
    .setsockopt = sm_alg_setsockopt,
    .getsockopt = sm_alg_getsockopt,
};

/* ============ AF_ALG算法类型定义 ============ */

static void *sm2_alg_bind(const char *name, u32 type, u32 mask)
{
    return strcmp(name, "sm2") ? ERR_PTR(-ENOENT) : NULL;
}

static void *sm3_alg_bind(const char *name, u32 type, u32 mask)
{
    return strcmp(name, "sm3") ? ERR_PTR(-ENOENT) : NULL;
}

static void *sm4_alg_bind(const char *name, u32 type, u32 mask)
{
    return strcmp(name, "sm4") ? ERR_PTR(-ENOENT) : NULL;
}

static void sm2_alg_release_parent(void *private)
{
    struct sm2_alg_ctx *ctx = private;
    if (ctx) {
        sm2_free_keypair(&ctx->keypair);
        kfree(ctx);
    }
}

static void sm_alg_release_parent(void *private)
{
    /* 通用的父级释放函数 */
    if (private)
        kfree(private);
}

static int sm2_alg_accept_parent(void *private, struct sock *sk)
{
    struct sm2_alg_ctx *ctx;

    ctx = kzalloc(sizeof(*ctx), GFP_KERNEL);
    if (!ctx)
        return -ENOMEM;

    /* 初始化SM2椭圆曲线上下文 */
    if (sm2_ec_ctx_init(&ctx->ec_ctx)) {
        kfree(ctx);
        return -EINVAL;
    }

    alg_sk(sk)->private = ctx;
    return 0;
}

static int sm3_alg_accept_parent(void *private, struct sock *sk)
{
    return sm3_alg_init(sk);
}

static int sm4_alg_accept_parent(void *private, struct sock *sk)
{
    struct sm4_alg_ctx *ctx;

    ctx = kzalloc(sizeof(*ctx), GFP_KERNEL);
    if (!ctx)
        return -ENOMEM;

    alg_sk(sk)->private = ctx;
    return 0;
}

/* 修正setkey函数签名 */
static int sm4_alg_setkey_parent(void *private, const u8 *key, unsigned int keylen)
{
    /* 这里需要根据实际的AF_ALG框架要求来实现 */
    return 0;
}

const struct af_alg_type sm2algtype = {
    .bind       = sm2_alg_bind,
    .release    = sm2_alg_release_parent,
    .accept     = sm2_alg_accept_parent,
    .ops        = &sm2_alg_ops,
    .name       = "sm2",
    .owner      = THIS_MODULE
};

const struct af_alg_type sm3algtype = {
    .bind       = sm3_alg_bind,
    .release    = sm_alg_release_parent,
    .accept     = sm3_alg_accept_parent,
    .ops        = &sm3_alg_ops,
    .name       = "sm3",
    .owner      = THIS_MODULE
};

const struct af_alg_type sm4algtype = {
    .bind       = sm4_alg_bind,
    .release    = sm_alg_release_parent,
    .accept     = sm4_alg_accept_parent,
    .setkey     = sm4_alg_setkey_parent,
    .ops        = &sm4_alg_ops,
    .name       = "sm4",
    .owner      = THIS_MODULE
};

/* ============ 模块初始化和清理 ============ */

int __init sm_af_alg_init(void)
{
    int ret;

    ret = af_alg_register_type(&sm2algtype);
    if (ret)
        goto out;

    ret = af_alg_register_type(&sm3algtype);
    if (ret)
        goto unreg_sm2;

    ret = af_alg_register_type(&sm4algtype);
    if (ret)
        goto unreg_sm3;

    printk(KERN_INFO "SM AF_ALG interface loaded successfully\n");
    return 0;

unreg_sm3:
    af_alg_unregister_type(&sm3algtype);
unreg_sm2:
    af_alg_unregister_type(&sm2algtype);
out:
    return ret;
}

void __exit sm_af_alg_exit(void)
{
    af_alg_unregister_type(&sm4algtype);
    af_alg_unregister_type(&sm3algtype);
    af_alg_unregister_type(&sm2algtype);
    printk(KERN_INFO "SM AF_ALG interface unloaded\n");
}

module_init(sm_af_alg_init);
module_exit(sm_af_alg_exit);

//MODULE_DEPEND("sm2");
//MODULE_DEPEND("sm3_generic"); 
//MODULE_DEPEND("sm4_generic");

MODULE_LICENSE("GPL");
MODULE_DESCRIPTION("国密算法AF_ALG接口实现");
MODULE_AUTHOR("Enhanced for strongSwan support");
MODULE_VERSION("1.0");
