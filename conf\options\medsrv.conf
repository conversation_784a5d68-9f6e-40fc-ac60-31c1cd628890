medsrv {

    # Mediation server database URI. If it contains a password, make sure to
    # adjust the permissions of the config file accordingly.
    # database =

    # Debugging in mediation server web application.
    # debug = no

    # DPD timeout to use in mediation server plugin.
    # dpd = 5m

    # Plugins to load in mediation server plugin.
    # load =

    # Minimum password length required for mediation server user accounts.
    # password_length = 6

    # Rekeying time on mediation connections in mediation server plugin.
    # rekey = 20m

    # Run Mediation server web application statically on socket.
    # socket =

    # Number of thread for mediation service web application.
    # threads = 5

    # Session timeout for mediation service.
    # timeout = 15m

}

