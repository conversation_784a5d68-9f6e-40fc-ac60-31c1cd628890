#!/bin/bash

# 诊断gmalg插件加载问题的脚本

echo "=== strongSwan gmalg插件诊断脚本 ==="
echo ""

# 1. 检查编译配置
echo "1. 检查编译配置..."
if [ -f "config.log" ]; then
    echo "   检查configure配置:"
    grep -i "gmalg" config.log | head -10
    echo ""
    echo "   检查AF_ALG支持:"
    grep -i "af_alg" config.log | head -5
    echo ""
else
    echo "   config.log 不存在，请先运行configure"
fi

# 2. 检查Makefile生成
echo "2. 检查Makefile生成..."
if [ -f "src/libstrongswan/Makefile" ]; then
    echo "   检查gmalg插件是否包含在libstrongswan中:"
    grep -i "gmalg" src/libstrongswan/Makefile | head -5
    echo ""
    echo "   检查USE_GMALG条件:"
    grep -i "USE_GMALG" src/libstrongswan/Makefile | head -3
    echo ""
else
    echo "   Makefile不存在，请先运行configure和make"
fi

# 3. 检查插件库文件
echo "3. 检查插件库文件..."
echo "   查找gmalg相关的库文件:"
find . -name "*gmalg*" -type f 2>/dev/null | head -10
echo ""

# 4. 检查安装的插件
echo "4. 检查安装的插件..."
if [ -d "/usr/lib/ipsec/plugins" ]; then
    echo "   /usr/lib/ipsec/plugins 目录内容:"
    ls -la /usr/lib/ipsec/plugins/ | grep -i gmalg || echo "   未找到gmalg插件"
    echo ""
fi

if [ -d "/usr/lib/strongswan/plugins" ]; then
    echo "   /usr/lib/strongswan/plugins 目录内容:"
    ls -la /usr/lib/strongswan/plugins/ | grep -i gmalg || echo "   未找到gmalg插件"
    echo ""
fi

# 5. 检查strongSwan配置
echo "5. 检查strongSwan配置..."
if [ -f "/etc/strongswan.conf" ]; then
    echo "   检查strongswan.conf中的插件配置:"
    grep -A 5 -B 5 -i "gmalg\|load.*=" /etc/strongswan.conf | head -20
    echo ""
fi

if [ -d "/etc/strongswan.d" ]; then
    echo "   检查strongswan.d目录中的gmalg配置:"
    find /etc/strongswan.d -name "*gmalg*" -type f 2>/dev/null
    if [ -f "/etc/strongswan.d/charon/gmalg.conf" ]; then
        echo "   gmalg.conf内容:"
        cat /etc/strongswan.d/charon/gmalg.conf
    fi
    echo ""
fi

# 6. 检查运行时状态
echo "6. 检查运行时状态..."
if command -v ipsec >/dev/null 2>&1; then
    echo "   strongSwan版本信息:"
    ipsec version
    echo ""
    
    echo "   检查插件加载状态:"
    ipsec statusall | grep -i "loaded plugins"
    echo ""
    
    echo "   检查是否有gmalg相关错误:"
    if command -v journalctl >/dev/null 2>&1; then
        journalctl -u strongswan --no-pager -n 20 | grep -i gmalg || echo "   未找到gmalg相关日志"
    fi
    echo ""
else
    echo "   ipsec命令不可用"
fi

# 7. 检查内核支持
echo "7. 检查内核支持..."
echo "   检查AF_ALG支持:"
if [ -e "/proc/crypto" ]; then
    echo "   可用的crypto算法:"
    grep "^name" /proc/crypto | grep -E "(sm2|sm3|sm4|SM2|SM3|SM4)" || echo "   未找到SM算法"
else
    echo "   /proc/crypto 不存在"
fi
echo ""

# 8. 检查编译错误
echo "8. 检查可能的编译问题..."
if [ -f "config.h" ]; then
    echo "   检查config.h中的gmalg相关定义:"
    grep -i "gmalg\|af_alg" config.h || echo "   未找到相关定义"
    echo ""
fi

# 9. 提供修复建议
echo "9. 修复建议..."
echo "   如果gmalg插件未正确编译或加载，请尝试:"
echo "   1. 重新运行configure，确保使用正确的参数:"
echo "      ./configure --prefix=/usr --sysconfdir=/etc --enable-stroke --enable-kernel-libipsec --enable-gmalg --with-gmalg_interior=yes --enable-openssl --enable-gmp"
echo ""
echo "   2. 检查编译日志中是否有错误:"
echo "      make 2>&1 | grep -i gmalg"
echo ""
echo "   3. 确保内核支持国密算法:"
echo "      cat /proc/crypto | grep -i sm"
echo ""
echo "   4. 检查插件配置文件:"
echo "      echo 'charon { plugins { gmalg { load = yes } } }' > /etc/strongswan.d/charon/gmalg.conf"
echo ""
echo "   5. 重启strongSwan服务:"
echo "      sudo systemctl restart strongswan"
echo ""

echo "=== 诊断完成 ==="
