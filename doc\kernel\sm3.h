/*
 * SM3安全哈希算法的通用值定义
 * 增强版本，同时支持原厂家SDK和strongSwan需求
 */

#ifndef _CRYPTO_SM3_H
#define _CRYPTO_SM3_H

#include <linux/types.h>

/* SM3算法常量定义 */
#define SM3_DIGEST_SIZE	32      /* SM3摘要长度：256位 */
#define SM3_BLOCK_SIZE	64      /* SM3块大小：512位 */

/* SM3算法中使用的常数 */
#define SM3_T1		0x79CC4519  /* 前16轮使用的常数 */
#define SM3_T2		0x7A879D8A  /* 后48轮使用的常数 */

/* SM3初始值（IV） */
#define SM3_IVA		0x7380166f
#define SM3_IVB		0x4914b2b9
#define SM3_IVC		0x172442d7
#define SM3_IVD		0xda8a0600
#define SM3_IVE		0xa96f30bc
#define SM3_IVF		0x163138aa
#define SM3_IVG		0xe38dee4d
#define SM3_IVH		0xb0fb0e4e

/* 零消息的SM3哈希值（预计算的空消息哈希结果） */
extern const u8 sm3_zero_message_hash[SM3_DIGEST_SIZE];

/* SM3状态结构体 */
struct sm3_state {
	u32 state[SM3_DIGEST_SIZE / 4];  /* 内部状态：8个32位字 */
	u64 count;                       /* 处理的字节总数 */
	u8 buffer[SM3_BLOCK_SIZE];       /* 输入缓冲区 */
};

/* strongSwan兼容的SM3上下文结构体 */
struct sm3_context {
	struct sm3_state state;  /* SM3状态 */
	bool finalized;          /* 是否已完成计算 */
};

struct shash_desc;

/* 原始函数声明（保持与Linux内核兼容性） */
extern int crypto_sm3_update(struct shash_desc *desc, const u8 *data,
			      unsigned int len);

extern int crypto_sm3_final(struct shash_desc *desc, u8 *out);

extern int crypto_sm3_finup(struct shash_desc *desc, const u8 *data,
			     unsigned int len, u8 *hash);

/* strongSwan兼容函数声明 */

/**
 * sm3_init_ctx - 初始化SM3上下文
 * @ctx: SM3上下文指针
 * 
 * 初始化SM3哈希计算上下文，设置初始状态值
 * 
 * 返回: 成功时返回0，失败时返回负错误码
 */
extern int sm3_init_ctx(struct sm3_context *ctx);

/**
 * sm3_update_ctx - 向SM3上下文添加数据
 * @ctx: SM3上下文指针
 * @data: 待哈希的数据
 * @len: 数据长度
 * 
 * 将数据添加到SM3哈希计算中，可以多次调用以处理大量数据
 * 
 * 返回: 成功时返回0，失败时返回负错误码
 */
extern int sm3_update_ctx(struct sm3_context *ctx, const u8 *data, unsigned int len);

/**
 * sm3_final_ctx - 完成SM3哈希计算
 * @ctx: SM3上下文指针
 * @out: 输出缓冲区，用于存储32字节的哈希值
 * 
 * 完成SM3哈希计算并输出最终的摘要值
 * 
 * 返回: 成功时返回0，失败时返回负错误码
 */
extern int sm3_final_ctx(struct sm3_context *ctx, u8 *out);

/**
 * sm3_hash - 一次性计算数据的SM3哈希值
 * @data: 待哈希的数据
 * @len: 数据长度
 * @out: 输出缓冲区，用于存储32字节的哈希值
 * 
 * 对给定数据进行完整的SM3哈希计算，适用于一次性处理小量数据
 * 
 * 返回: 成功时返回0，失败时返回负错误码
 */
extern int sm3_hash(const u8 *data, unsigned int len, u8 *out);

/**
 * sm3_hmac - 计算基于SM3的HMAC值
 * @key: HMAC密钥
 * @key_len: 密钥长度
 * @data: 待认证的数据
 * @data_len: 数据长度
 * @out: 输出缓冲区，用于存储32字节的HMAC值
 * 
 * 使用SM3哈希算法计算HMAC（Hash-based Message Authentication Code）
 * 符合RFC 2104标准的HMAC实现
 * 
 * 返回: 成功时返回0，失败时返回负错误码
 */
extern int sm3_hmac(const u8 *key, unsigned int key_len,
	            const u8 *data, unsigned int data_len, u8 *out);

/**
 * sm3_kdf - SM3密钥派生函数
 * @input: 输入种子材料
 * @input_len: 输入长度
 * @output: 输出缓冲区
 * @output_len: 所需输出长度
 * 
 * 基于SM3实现的密钥派生函数，符合GM/T 0003.4-2012标准
 * 可以从较短的种子材料生成任意长度的密钥材料
 * 
 * 返回: 成功时返回0，失败时返回负错误码
 */
extern int sm3_kdf(const u8 *input, unsigned int input_len, 
	           u8 *output, unsigned int output_len);

#endif /* _CRYPTO_SM3_H */