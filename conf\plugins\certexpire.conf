certexpire {

    # Whether to load the plugin. Can also be an integer to increase the
    # priority of this plugin.
    load = yes

    csv {

        # Cron style string specifying CSV export times.
        # cron =

        # String to use in empty intermediate CA fields.
        # empty_string =

        # Use a fixed intermediate CA field count.
        # fixed_fields = yes

        # Force export of all trustchains we have a private key for.
        # force = yes

        # strftime(3) format string to export expiration dates as.
        # format = %d:%m:%Y

        # strftime(3) format string for the CSV file name to export local
        # certificates to.
        # local =

        # strftime(3) format string for the CSV file name to export remote
        # certificates to.
        # remote =

        # CSV field separator.
        # separator = ,

    }

}

