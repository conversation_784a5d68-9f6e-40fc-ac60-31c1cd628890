#include <stdio.h>
#include <string.h>
#include <unistd.h>
#include <fcntl.h>
#include <stdlib.h>

#include "sm2.h"
#include "debug.h"

u8 _random[ECC_NUMWORD] = {
	0x6F, 0xF9, 0xB2, 0x1F, 0xAE, 0xBA, 0x0D, 0x26, 0x27, 0xB7, 0x72, 0xDD, 0x25, 0xD9, 0x76, 0xC1,
	0x3F, 0x66, 0x17, 0x48, 0x93, 0x4E, 0xF9, 0x94, 0x5C, 0x17, 0x5C, 0x38, 0x99, 0x8D, 0xB2, 0x6C};

ecc_point pub = {
	.x = {0x09,0xF9,0xDF,0x31,0x1E,0x54,0x21,0xA1,0x50,0xDD,0x7D,0x16,0x1E,0x4B,0xC5,0xC6,
		0x72,0x17,0x9F,0xAD,0x18,0x33,0xFC,0x07,0x6B,0xB0,0x8F,0xF3,0x56,0xF3,0x50,0x20},

	.y = {0xCC,0xEA,0x49,0x0C,0xE2,0x67,0x75,0xA5,0x2D,0xC6,0xEA,0x71,0x8C,0xC1,0xAA,0x60,
		0x0A,0xED,0x05,0xFB,0xF3,0x5E,0x08,0x4A,0x66,0x32,0xF6,0x07,0x2D,0xA9,0xAD,0x13}};

u8 pri[ECC_NUMWORD] = {
	0x39,0x45,0x20,0x8F,0x7B,0x21,0x44,0xB1,0x3F,0x36,0xE3,0x8A,0xC6,0xD3,0x9F,0x95,
	0x88,0x93,0x93,0x69,0x28,0x60,0xB5,0x1A,0x42,0xFB,0x81,0xEF,0x4D,0xF7,0xC5,0xB8};

u8 hash[ECC_NUMWORD] = {
	0x95, 0x8E, 0x72, 0xE6, 0x3C, 0x1B, 0x65, 0xD3, 0x25, 0xAC, 0xF7, 0xF6, 0x50, 0xAF, 0xBA, 0x75,
	0x32, 0x5E, 0x22, 0x47, 0x58, 0xB0, 0x7C, 0x10, 0x66, 0xBB, 0xC1, 0x5A, 0xC5, 0x46, 0x89, 0xED};

u8 C[ECC_NUMWORD*5];
u8 M[ECC_NUMWORD*2] = {
	0x65, 0x6E, 0x63, 0x72,  0x79, 0x70, 0x74, 0x69,  0x6F, 0x6E, 0x20, 0x73,  0x74, 0x61, 0x6E, 0x64,
	0x61, 0x72, 0x64};

int main(int argc, char **argv)
{
	u8 r[ECC_NUMWORD];
	u8 s[ECC_NUMWORD];
	int ret = -1;
	int Clen;
	int Mlen;
	int i = 1;

	sm2_make_prikey(pri);
	sm2_make_pubkey(pri, &pub);

	sm2_sign(r, s, pri, hash);
	printHex("r", r, 32);
	printHex("s", s, 32);
	ret = sm2_verify(&pub, hash, r, s);
	if (ret)
		printf("verify err ret = %d\n", ret);
	else
		printf("verify ok\n");

	sm2_encrypt(&pub, M, 19, C, &Clen);
	printHex("C", C, Clen);
	memset(M, 0, 64);
	ret = sm2_decrypt(pri, C, Clen, M, &Mlen);
	if(ret)
		printf(" decrypt err \n");
	else
		printHex("M", M, Mlen);

	return 0;
}
