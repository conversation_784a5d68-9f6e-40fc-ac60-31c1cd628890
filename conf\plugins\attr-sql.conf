attr-sql {

    # Release all online leases during startup.  Disable this to share the DB
    # between multiple VPN gateways.
    # crash_recovery = yes

    # Database URI for attr-sql plugin used by charon. If it contains a
    # password, make sure to adjust the permissions of the config file
    # accordingly.
    # database =

    # Enable logging of SQL IP pool leases.
    # lease_history = yes

    # Whether to load the plugin. Can also be an integer to increase the
    # priority of this plugin.
    load = yes

}

