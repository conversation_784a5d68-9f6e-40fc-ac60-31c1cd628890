dhcp {

    # Always use the configured server address.
    # force_server_address = no

    # Derive user-defined MAC address from hash of IKE identity and send client
    # identity DHCP option.
    # identity_lease = no

    # Interface name the plugin uses for address allocation.
    # interface =

    # Whether to load the plugin. Can also be an integer to increase the
    # priority of this plugin.
    load = yes

    # DHCP server unicast or broadcast IP address.
    # server = ***************

    # Use the DHCP server port (67) as source port when a unicast server address
    # is configured.
    # use_server_port = no

}

