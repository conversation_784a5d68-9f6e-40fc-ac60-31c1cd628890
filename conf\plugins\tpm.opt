charon.plugins.tpm.use_rng = no
	Whether the TPM should be used as RNG.

charon.plugins.tpm.fips_186_4 = no
	Is the TPM 2.0 FIPS-186-4 compliant, forcing e.g. the use of the default
	salt length instead of maximum salt length with RSAPSS padding.

charon.plugins.tpm.tcti.name = device|tabrmd
	Name of TPM 2.0 TCTI library. Valid values: _tabrmd_, _device_ or _mssim_.
	Defaults are _device_ if the _/dev/tpmrm0_ in-kernel TPM 2.0 resource manager
	device exists, and _tabrmd_ otherwise, requiring the d-bus based TPM 2.0
	access broker and resource manager to be available.

charon.plugins.tpm.tcti.opts = /dev/tpmrm0|<none>
	Options for the TPM 2.0 TCTI library. Defaults are _/dev/tpmrm0_ if the
	TCTI library name is _device_ and no options otherwise.
