/* SPDX-License-Identifier: GPL-2.0-or-later */
/*
 * SM2 非对称公钥算法完整实现
 * 基于 OSCCA GM/T 0003.1-2012 -- 0003.5-2012 SM2 标准
 * 完整版本，同时支持原厂家SDK和strongSwan需求
 *
 * Copyright (c) 2025, Seewor
 * Authors: <AUTHORS>
 * Enhanced for strongSwan support
 */

#include <linux/module.h>
#include <linux/mpi.h>
#include <crypto/internal/akcipher.h>
#include <crypto/akcipher.h>
#include <crypto/hash.h>
#include <crypto/sm3_base.h>
#include <crypto/rng.h>
#include <crypto/sm2.h>
#include <linux/random.h>
#include <linux/scatterlist.h>
#include "sm2signature.asn1.h"

#define MPI_NBYTES(m)   ((mpi_get_nbits(m) + 7) / 8)

///* strongSwan兼容性结构定义 */
//struct sm2_key_pair {
//    MPI private_key;    /* 私钥 */
//    MPI_POINT public_key;   /* 公钥点 */
//};
//
//struct sm2_signature {
//    MPI r;  /* 签名r分量 */
//    MPI s;  /* 签名s分量 */
//};

/* SM2加密结果结构 */
struct sm2_ciphertext {
    MPI_POINT c1;      /* 椭圆曲线点C1 */
    u8 c2[256];        /* 密文数据C2 */
    u8 c3[32];         /* SM3哈希值C3 */
    size_t c2_len;     /* C2的实际长度 */
};

/* ECC域参数结构定义 */
struct ecc_domain_parms {
	const char *desc;           /* 曲线描述 */
	unsigned int nbits;         /* 位数 */
	unsigned int fips:1;        /* 是否为FIPS140-2认证曲线 */

	/* 描述此曲线的模型，主要用于选择群方程 */
	enum gcry_mpi_ec_models model;

	/* 实际使用的ECC方言，用于曲线特定优化和选择编码等 */
	enum ecc_dialects dialect;

	const char *p;              /* 定义域的素数 */
	const char *a, *b;          /* 系数，对于扭曲爱德华曲线，b用于d
				                 * 对于蒙哥马利曲线 (a,b) 为 ((A-2)/4,B^-1) */
	const char *n;              /* 基点的阶 */
	const char *g_x, *g_y;      /* 基点坐标 */
	unsigned int h;             /* 余因子 */
};

/* SM2推荐曲线参数 */
static const struct ecc_domain_parms sm2_ecp = {
	.desc = "sm2p256v1",
	.nbits = 256,
	.fips = 0,
	.model = MPI_EC_WEIERSTRASS,
	.dialect = ECC_DIALECT_STANDARD,
	.p   = "0xfffffffeffffffffffffffffffffffffffffffff00000000ffffffffffffffff",
	.a   = "0xfffffffeffffffffffffffffffffffffffffffff00000000fffffffffffffffc",
	.b   = "0x28e9fa9e9d9f5e344d5a9e4bcf6509a7f39789f515ab8f92ddbcbd414d940e93",
	.n   = "0xfffffffeffffffffffffffffffffffff7203df6b21c6052b53bbf40939d54123",
	.g_x = "0x32c4ae2c1f1981195f9904466a39c9948fe30bbff2660be1715a4589334c74c7",
	.g_y = "0xbc3736a2f4f6779c59bdcee36b692153d0a9877cc62a474002df32e52139f0a0",
	.h = 1
};

/* 默认用户ID */
static const u8 default_user_id[] = {
	0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38,
	0x31, 0x32, 0x33, 0x34, 0x35, 0x36, 0x37, 0x38
};

/* 初始化SM2椭圆曲线上下文 */
int sm2_ec_ctx_init(struct mpi_ec_ctx *ec)
{
	const struct ecc_domain_parms *ecp = &sm2_ecp;
	MPI p, a, b;
	MPI x, y;
	int rc = -EINVAL;

	/* 解析曲线参数 */
	p = mpi_scanval(ecp->p);
	a = mpi_scanval(ecp->a);
	b = mpi_scanval(ecp->b);
	if (!p || !a || !b)
		goto free_p;

	/* 解析基点坐标 */
	x = mpi_scanval(ecp->g_x);
	y = mpi_scanval(ecp->g_y);
	if (!x || !y)
		goto free;

	rc = -ENOMEM;

	/* 分配公钥点内存 */
	ec->Q = mpi_point_new(0);
	if (!ec->Q)
		goto free;

	/* 设置椭圆曲线基点 */
	ec->G = mpi_point_new(0);
	if (!ec->G) {
		mpi_point_release(ec->Q);
		goto free;
	}

	mpi_set(ec->G->x, x);
	mpi_set(ec->G->y, y);
	mpi_set_ui(ec->G->z, 1);

	/* 设置曲线阶 */
	rc = -EINVAL;
	ec->n = mpi_scanval(ecp->n);
	if (!ec->n) {
		mpi_point_release(ec->Q);
		mpi_point_release(ec->G);
		goto free;
	}

	/* 初始化椭圆曲线参数 */
	ec->h = ecp->h;
	ec->name = ecp->desc;
	mpi_ec_init(ec, ecp->model, ecp->dialect, 0, p, a, b);

	rc = 0;

free:
	mpi_free(x);
	mpi_free(y);
free_p:
	mpi_free(p);
	mpi_free(a);
	mpi_free(b);

	return rc;
}
EXPORT_SYMBOL(sm2_ec_ctx_init);

/* 释放SM2椭圆曲线上下文 */
static void sm2_ec_ctx_deinit(struct mpi_ec_ctx *ec)
{
	mpi_ec_deinit(ec);
	memset(ec, 0, sizeof(*ec));
}

/* 将八进制字符串转换为椭圆曲线点 */
static int sm2_ecc_os2ec(MPI_POINT result, MPI value)
{
	int rc;
	size_t n;
	unsigned char *buf;
	MPI x, y;

	n = MPI_NBYTES(value);
	buf = kmalloc(n, GFP_KERNEL);
	if (!buf)
		return -ENOMEM;

	rc = mpi_print(GCRYMPI_FMT_USG, buf, n, &n, value);
	if (rc)
		goto err_freebuf;

	rc = -EINVAL;
	if (n < 1 || ((n - 1) % 2))
		goto err_freebuf;
	/* 不支持点压缩 */
	if (*buf != 0x4)
		goto err_freebuf;

	rc = -ENOMEM;
	n = (n - 1) / 2;
	x = mpi_read_raw_data(buf + 1, n);
	if (!x)
		goto err_freebuf;
	y = mpi_read_raw_data(buf + 1 + n, n);
	if (!y)
		goto err_freex;

	mpi_normalize(x);
	mpi_normalize(y);
	mpi_set(result->x, x);
	mpi_set(result->y, y);
	mpi_set_ui(result->z, 1);

	rc = 0;

	mpi_free(y);
err_freex:
	mpi_free(x);
err_freebuf:
	kfree(buf);
	return rc;
}

/* SM2签名上下文结构 */
struct sm2_signature_ctx {
	MPI sig_r;  /* 签名r分量 */
	MPI sig_s;  /* 签名s分量 */
};

/* 获取签名的r分量 */
int sm2_get_signature_r(void *context, size_t hdrlen, unsigned char tag,
				const void *value, size_t vlen)
{
	struct sm2_signature_ctx *sig = context;

	if (!value || !vlen)
		return -EINVAL;

	sig->sig_r = mpi_read_raw_data(value, vlen);
	if (!sig->sig_r)
		return -ENOMEM;

	return 0;
}

/* 获取签名的s分量 */
int sm2_get_signature_s(void *context, size_t hdrlen, unsigned char tag,
				const void *value, size_t vlen)
{
	struct sm2_signature_ctx *sig = context;

	if (!value || !vlen)
		return -EINVAL;

	sig->sig_s = mpi_read_raw_data(value, vlen);
	if (!sig->sig_s)
		return -ENOMEM;

	return 0;
}

/* 更新Z值摘要计算（用于SM2签名预处理） */
static int sm2_z_digest_update(struct shash_desc *desc,
			MPI m, unsigned int pbytes)
{
	static const unsigned char zero[32];
	unsigned char *in;
	unsigned int inlen;

	in = mpi_get_buffer(m, &inlen, NULL);
	if (!in)
		return -EINVAL;

	/* 根据需要进行零填充或截断 */
	if (inlen < pbytes) {
		/* 用零填充 */
		crypto_sm3_update(desc, zero, pbytes - inlen);
		crypto_sm3_update(desc, in, inlen);
	} else if (inlen > pbytes) {
		/* 跳过开头的零 */
		crypto_sm3_update(desc, in + inlen - pbytes, pbytes);
	} else {
		crypto_sm3_update(desc, in, inlen);
	}

	kfree(in);
	return 0;
}

/* 更新椭圆曲线点的Z值摘要计算 */
static int sm2_z_digest_update_point(struct shash_desc *desc,
		MPI_POINT point, struct mpi_ec_ctx *ec, unsigned int pbytes)
{
	MPI x, y;
	int ret = -EINVAL;

	x = mpi_new(0);
	y = mpi_new(0);

	/* 获取仿射坐标并更新摘要 */
	if (!mpi_ec_get_affine(x, y, point, ec) &&
		!sm2_z_digest_update(desc, x, pbytes) &&
		!sm2_z_digest_update(desc, y, pbytes))
		ret = 0;

	mpi_free(x);
	mpi_free(y);
	return ret;
}

/* 计算Z值摘要（SM2签名预处理函数） */
int sm2_compute_z_digest(struct crypto_akcipher *tfm,
			const unsigned char *id, size_t id_len,
			unsigned char dgst[SM3_DIGEST_SIZE])
{
	struct mpi_ec_ctx *ec = akcipher_tfm_ctx(tfm);
	uint16_t bits_len;
	unsigned char entl[2];
	SHASH_DESC_ON_STACK(desc, NULL);
	unsigned int pbytes;

	if (id_len > (USHRT_MAX / 8) || !ec->Q)
		return -EINVAL;

	/* 计算ID长度的位数 */
	bits_len = (uint16_t)(id_len * 8);
	entl[0] = bits_len >> 8;
	entl[1] = bits_len & 0xff;

	pbytes = MPI_NBYTES(ec->p);

	/* ZA = H256(ENTLA | IDA | a | b | xG | yG | xA | yA) */
	sm3_base_init(desc);
	crypto_sm3_update(desc, entl, 2);
	crypto_sm3_update(desc, id, id_len);

	/* 依次添加曲线参数和点坐标 */
	if (sm2_z_digest_update(desc, ec->a, pbytes) ||
		sm2_z_digest_update(desc, ec->b, pbytes) ||
		sm2_z_digest_update_point(desc, ec->G, ec, pbytes) ||
		sm2_z_digest_update_point(desc, ec->Q, ec, pbytes))
		return -EINVAL;

	crypto_sm3_final(desc, dgst);
	return 0;
}
EXPORT_SYMBOL(sm2_compute_z_digest);

/* 计算SM2的Z值 */
static int sm2_compute_z_value(struct mpi_ec_ctx *ec, MPI_POINT public_key,
			       const u8 *user_id, size_t user_id_len, u8 *z_value)
{
	SHASH_DESC_ON_STACK(desc, NULL);
	u16 id_bits_len;
	u8 entl[2];
	unsigned int pbytes;
	int ret;

	if (!user_id) {
		user_id = default_user_id;
		user_id_len = sizeof(default_user_id);
	}

	/* 计算用户ID的位长度 */
	id_bits_len = user_id_len * 8;
	entl[0] = (id_bits_len >> 8) & 0xff;
	entl[1] = id_bits_len & 0xff;

	pbytes = MPI_NBYTES(ec->p);

	/* 计算Z = H(ENTLA || IDA || a || b || xG || yG || xA || yA) */
	sm3_base_init(desc);
	crypto_sm3_update(desc, entl, 2);
	crypto_sm3_update(desc, user_id, user_id_len);

	ret = sm2_z_digest_update(desc, ec->a, pbytes);
	if (ret)
		return ret;

	ret = sm2_z_digest_update(desc, ec->b, pbytes);
	if (ret)
		return ret;

	ret = sm2_z_digest_update_point(desc, ec->G, ec, pbytes);
	if (ret)
		return ret;

	ret = sm2_z_digest_update_point(desc, public_key, ec, pbytes);
	if (ret)
		return ret;

	crypto_sm3_final(desc, z_value);
	return 0;
}

/* SM2密钥派生函数KDF */
static int sm2_kdf(const u8 *shared_info, size_t shared_len, u8 *key_data, size_t key_len)
{
	SHASH_DESC_ON_STACK(desc, NULL);
	u32 counter = 1;
	u8 counter_bytes[4];
	u8 hash_result[SM3_DIGEST_SIZE];
	size_t generated = 0;
	size_t copy_len;

	while (generated < key_len) {
		/* 准备计数器（大端序） */
		counter_bytes[0] = (counter >> 24) & 0xff;
		counter_bytes[1] = (counter >> 16) & 0xff;
		counter_bytes[2] = (counter >> 8) & 0xff;
		counter_bytes[3] = counter & 0xff;

		/* 计算 Hash(shared_info || counter) */
		sm3_base_init(desc);
		crypto_sm3_update(desc, shared_info, shared_len);
		crypto_sm3_update(desc, counter_bytes, 4);
		crypto_sm3_final(desc, hash_result);

		/* 复制哈希结果到输出 */
		copy_len = min(key_len - generated, (size_t)SM3_DIGEST_SIZE);
		memcpy(key_data + generated, hash_result, copy_len);
		generated += copy_len;
		counter++;
	}

	/* 清除敏感数据 */
	memzero_explicit(hash_result, sizeof(hash_result));
	return 0;
}

/* 检查椭圆曲线点是否有效 */
static bool sm2_point_is_valid(struct mpi_ec_ctx *ec, MPI_POINT point)
{
	MPI left, right, temp1, temp2;
	bool valid = false;

	/* 检查点是否在无穷远 */
	if (mpi_ec_get_affine(NULL, NULL, point, ec))
		return false;

	left = mpi_new(0);
	right = mpi_new(0);
	temp1 = mpi_new(0);
	temp2 = mpi_new(0);

	if (!left || !right || !temp1 || !temp2)
		goto cleanup;

	/* 验证点是否在椭圆曲线上：y^2 = x^3 + ax + b */
	/* 计算 left = y^2 */
	mpi_powm(left, point->y, mpi_const(MPI_C_TWO), ec->p);

	/* 计算 right = x^3 + ax + b */
	mpi_powm(temp1, point->x, mpi_const(MPI_C_THREE), ec->p);
	mpi_mulm(temp2, ec->a, point->x, ec->p);
	mpi_addm(right, temp1, temp2, ec->p);
	mpi_addm(right, right, ec->b, ec->p);

	/* 检查 left == right */
	if (mpi_cmp(left, right) == 0)
		valid = true;

cleanup:
	mpi_free(left);
	mpi_free(right);
	mpi_free(temp1);
	mpi_free(temp2);
	return valid;
}

/* SM2签名验证核心函数 */
static int _sm2_verify(struct mpi_ec_ctx *ec, MPI hash, MPI sig_r, MPI sig_s)
{
	int rc = -EINVAL;
	struct gcry_mpi_point sG, tP;
	MPI t = NULL;
	MPI x1 = NULL, y1 = NULL;

	/* 初始化点和MPI */
	mpi_point_init(&sG);
	mpi_point_init(&tP);
	x1 = mpi_new(0);
	y1 = mpi_new(0);
	t = mpi_new(0);

	/* 验证r, s在[1, n-1]范围内 */
	if (mpi_cmp_ui(sig_r, 1) < 0 || mpi_cmp(sig_r, ec->n) >= 0 ||
		mpi_cmp_ui(sig_s, 1) < 0 || mpi_cmp(sig_s, ec->n) >= 0) {
		goto leave;
	}

	/* 计算t = (r + s) % n，检查t != 0 */
	mpi_addm(t, sig_r, sig_s, ec->n);
	if (mpi_cmp_ui(t, 0) == 0)
		goto leave;

	/* 计算sG + tP = (x1, y1) */
	rc = -EBADMSG;
	mpi_ec_mul_point(&sG, sig_s, ec->G, ec);
	mpi_ec_mul_point(&tP, t, ec->Q, ec);
	mpi_ec_add_points(&sG, &sG, &tP, ec);
	if (mpi_ec_get_affine(x1, y1, &sG, ec))
		goto leave;

	/* 计算R = (e + x1) % n */
	mpi_addm(t, hash, x1, ec->n);

	/* 检查R == r */
	rc = -EKEYREJECTED;
	if (mpi_cmp(t, sig_r))
		goto leave;

	rc = 0;

leave:
	mpi_point_free_parts(&sG);
	mpi_point_free_parts(&tP);
	mpi_free(x1);
	mpi_free(y1);
	mpi_free(t);

	return rc;
}

/* strongSwan兼容的SM2密钥生成函数 */
int sm2_generate_keypair(struct mpi_ec_ctx *ec, struct sm2_key_pair *keypair)
{
	MPI private_key;
	u8 random_bytes[32];
	int rc, attempts = 10;

	/* 生成私钥（随机数） */
	private_key = mpi_new(256);
	if (!private_key)
		return -ENOMEM;

	/* 分配公钥点 */
	keypair->public_key = mpi_point_new(0);
	if (!keypair->public_key) {
		mpi_free(private_key);
		return -ENOMEM;
	}

	/* 生成随机私钥，确保在[1, n-1]范围内 */
	do {
		get_random_bytes(random_bytes, 32);
		rc = mpi_read_buffer(private_key, random_bytes, 32, NULL, NULL);
		if (rc)
			goto error;

		mpi_normalize(private_key);
		
		/* 检查私钥是否在有效范围内 */
		if (mpi_cmp_ui(private_key, 1) >= 0 && mpi_cmp(private_key, ec->n) < 0)
			break;
			
	} while (--attempts > 0);

	if (attempts == 0) {
		rc = -EAGAIN;
		goto error;
	}

	/* 计算公钥点 Q = dG */
	mpi_ec_mul_point(keypair->public_key, private_key, ec->G, ec);
	
	/* 验证生成的公钥点是否有效 */
	if (!sm2_point_is_valid(ec, keypair->public_key)) {
		rc = -EINVAL;
		goto error;
	}

	keypair->private_key = private_key;

	/* 清除敏感数据 */
	memzero_explicit(random_bytes, sizeof(random_bytes));
	return 0;

error:
	mpi_free(private_key);
	mpi_point_release(keypair->public_key);
	memzero_explicit(random_bytes, sizeof(random_bytes));
	return rc;
}
EXPORT_SYMBOL(sm2_generate_keypair);

/* strongSwan兼容的SM2签名函数 */
int sm2_sign_data(struct mpi_ec_ctx *ec, MPI private_key, 
		  const unsigned char *data, size_t data_len,
		  const unsigned char *user_id, size_t user_id_len,
		  struct sm2_signature *signature)
{
	MPI e, k, r, s, temp, one;
	MPI_POINT kG;
	u8 z_value[SM3_DIGEST_SIZE];
	//u8 hash_input[SM3_DIGEST_SIZE + data_len];
	
	u8 final_hash[SM3_DIGEST_SIZE];
	u8 random_bytes[32];
	SHASH_DESC_ON_STACK(desc, NULL);
	int rc, attempts = 10;

	/* 获取公钥点进行Z值计算 */
	MPI_POINT public_key = mpi_point_new(0);
	
	u8 *hash_input = kmalloc(SM3_DIGEST_SIZE + data_len, GFP_KERNEL);
	if (!hash_input)
		return -ENOMEM;
	
	if (!public_key)
		return -ENOMEM;

	mpi_ec_mul_point(public_key, private_key, ec->G, ec);

	/* 计算Z值 */
	rc = sm2_compute_z_value(ec, public_key, user_id, user_id_len, z_value);
	if (rc)
		goto cleanup_pubkey;

	/* 计算e = H(Z || M) */
	sm3_base_init(desc);
	crypto_sm3_update(desc, z_value, SM3_DIGEST_SIZE);
	crypto_sm3_update(desc, data, data_len);
	crypto_sm3_final(desc, final_hash);

	/* 分配临时变量 */
	e = mpi_new(256);
	k = mpi_new(256);
	r = mpi_new(256);
	s = mpi_new(256);
	temp = mpi_new(256);
	one = mpi_new(8);
	kG = mpi_point_new(0);

	if (!e || !k || !r || !s || !temp || !one || !kG) {
		rc = -ENOMEM;
		goto free_all;
	}

	mpi_set_ui(one, 1);

	/* 将哈希转换为MPI */
	rc = mpi_read_buffer(e, final_hash, SM3_DIGEST_SIZE, NULL, NULL);
	if (rc)
		goto free_all;

	/* SM2签名算法主循环 */
	do {
		/* 生成随机数k */
		do {
			get_random_bytes(random_bytes, 32);
			rc = mpi_read_buffer(k, random_bytes, 32, NULL, NULL);
			if (rc)
				goto free_all;
			mpi_normalize(k);
		} while (mpi_cmp_ui(k, 1) < 0 || mpi_cmp(k, ec->n) >= 0);

		/* 计算(x1, y1) = kG */
		mpi_ec_mul_point(kG, k, ec->G, ec);
		
		/* 计算r = (e + x1) mod n */
		mpi_addm(r, e, kG->x, ec->n);

		/* 检查r != 0 && r + k != n */
		if (mpi_cmp_ui(r, 0) == 0)
			continue;
		
		mpi_addm(temp, r, k, ec->n);
		if (mpi_cmp(temp, ec->n) == 0)
			continue;

		/* 计算s = (1 + d)^(-1) * (k - rd) mod n */
		mpi_addm(temp, private_key, one, ec->n);  /* temp = 1 + d */
		mpi_invm(temp, temp, ec->n);              /* temp = (1 + d)^(-1) */
		
		mpi_mulm(s, r, private_key, ec->n);       /* s = r * d */
		mpi_subm(s, k, s, ec->n);                 /* s = k - r * d */
		mpi_mulm(s, temp, s, ec->n);              /* s = (1 + d)^(-1) * (k - r * d) */

		/* 检查s != 0 */
		if (mpi_cmp_ui(s, 0) != 0)
			break;
			
	} while (--attempts > 0);

	if (attempts == 0) {
		rc = -EAGAIN;
		goto free_all;
	}

	/* 设置签名结果 */
	signature->r = mpi_copy(r);
	signature->s = mpi_copy(s);
	
	if (!signature->r || !signature->s) {
		rc = -ENOMEM;
		mpi_free(signature->r);
		mpi_free(signature->s);
		goto free_all;
	}

	rc = 0;

free_all:
	mpi_free(e);
	mpi_free(k);
	mpi_free(r);
	mpi_free(s);
	mpi_free(temp);
	mpi_free(one);
	mpi_point_release(kG);
cleanup_pubkey:
	mpi_point_release(public_key);
	
	/* 清除敏感数据 */
	memzero_explicit(z_value, sizeof(z_value));
	memzero_explicit(final_hash, sizeof(final_hash));
	memzero_explicit(random_bytes, sizeof(random_bytes));

	return rc;
}
EXPORT_SYMBOL(sm2_sign_data);

/* strongSwan兼容的SM2验证函数 */
int sm2_verify_signature(struct mpi_ec_ctx *ec, MPI_POINT public_key,
			 const unsigned char *data, size_t data_len,
			 const unsigned char *user_id, size_t user_id_len,
			 struct sm2_signature *signature)
{
	MPI e;
	u8 z_value[SM3_DIGEST_SIZE];
	u8 final_hash[SM3_DIGEST_SIZE];
	SHASH_DESC_ON_STACK(desc, NULL);
	int rc;

	/* 验证公钥点是否有效 */
	if (!sm2_point_is_valid(ec, public_key))
		return -EINVAL;

	/* 计算Z值 */
	rc = sm2_compute_z_value(ec, public_key, user_id, user_id_len, z_value);
	if (rc)
		return rc;

	/* 计算e = H(Z || M) */
	sm3_base_init(desc);
	crypto_sm3_update(desc, z_value, SM3_DIGEST_SIZE);
	crypto_sm3_update(desc, data, data_len);
	crypto_sm3_final(desc, final_hash);

	/* 将哈希转换为MPI */
	e = mpi_new(256);
	if (!e) {
		rc = -ENOMEM;
		goto cleanup;
	}

	rc = mpi_read_buffer(e, final_hash, SM3_DIGEST_SIZE, NULL, NULL);
	if (rc)
		goto free_e;

	/* 临时设置公钥到椭圆曲线上下文 */
	//mpi_point_set(ec->Q, public_key);
	
	mpi_set(ec->Q->x, public_key->x);
	mpi_set(ec->Q->y, public_key->y);
	mpi_set(ec->Q->z, public_key->z);

	/* 调用核心验证函数 */
	rc = _sm2_verify(ec, e, signature->r, signature->s);

free_e:
	mpi_free(e);
cleanup:
	/* 清除敏感数据 */
	memzero_explicit(z_value, sizeof(z_value));
	memzero_explicit(final_hash, sizeof(final_hash));
	return rc;
}
EXPORT_SYMBOL(sm2_verify_signature);

/* strongSwan兼容的SM2加密函数 */
int sm2_encrypt_data(struct mpi_ec_ctx *ec, MPI_POINT public_key,
		     const unsigned char *plaintext, size_t plaintext_len,
		     unsigned char *ciphertext, size_t *ciphertext_len)
{
	MPI k;
	MPI_POINT c1, kpb;
	u8 x2y2[64];  /* x2 || y2 */
	u8 *t;        /* 密钥流 */
	u8 c3[SM3_DIGEST_SIZE];
	u8 random_bytes[32];
	SHASH_DESC_ON_STACK(desc, NULL);
	size_t required_len;
	int rc, attempts = 10;
	int i;
	
	/* 提取x2和y2的字节表示 */
	unsigned char *x2_buf, *y2_buf;
	unsigned int x2_len, y2_len;
	/* 提取C1的坐标 */
	unsigned char *c1x_buf, *c1y_buf;
	unsigned int c1x_len, c1y_len;
	bool all_zero = true;
	
	u8 *c2 = ciphertext + 1 + 64 + 32;  /* C2在密文中的位置 */

	/* 检查明文长度 */
	if (plaintext_len > SM2_MAX_PLAINTEXT_SIZE)
		return -EINVAL;

	/* 验证公钥点是否有效 */
	if (!sm2_point_is_valid(ec, public_key))
		return -EINVAL;

	/* 计算所需的密文长度：1 + 64 + 32 + plaintext_len */
	required_len = 1 + 64 + 32 + plaintext_len;
	if (*ciphertext_len < required_len) {
		*ciphertext_len = required_len;
		return -ENOBUFS;
	}

	/* 分配必要的变量 */
	k = mpi_new(256);
	c1 = mpi_point_new(0);
	kpb = mpi_point_new(0);
	t = kmalloc(plaintext_len, GFP_KERNEL);

	if (!k || !c1 || !kpb || !t) {
		rc = -ENOMEM;
		goto free_all;
	}

	do {
		/* 生成随机数k */
		do {
			get_random_bytes(random_bytes, 32);
			rc = mpi_read_buffer(k, random_bytes, 32, NULL, NULL);
			if (rc)
				goto free_all;
			mpi_normalize(k);
		} while (mpi_cmp_ui(k, 1) < 0 || mpi_cmp(k, ec->n) >= 0);

		/* 计算C1 = kG */
		mpi_ec_mul_point(c1, k, ec->G, ec);

		/* 验证C1不是无穷远点 */
		if (mpi_ec_get_affine(NULL, NULL, c1, ec))
			continue;

		/* 计算S = h * Pb，验证S不是无穷远点（h=1对SM2） */
		/* 对于SM2曲线，h=1，所以S=Pb，只需验证Pb有效即可 */

		/* 计算kPb = k * Pb */
		mpi_ec_mul_point(kpb, k, public_key, ec);

		/* 获取kPb的仿射坐标 */
		rc = mpi_ec_get_affine(NULL, NULL, kpb, ec);
		if (rc)
			continue;

		

		x2_buf = mpi_get_buffer(kpb->x, &x2_len, NULL);
		y2_buf = mpi_get_buffer(kpb->y, &y2_len, NULL);

		if (!x2_buf || !y2_buf) {
			kfree(x2_buf);
			kfree(y2_buf);
			rc = -ENOMEM;
			goto free_all;
		}

		/* 将x2和y2拷贝到x2y2数组中，确保32字节对齐 */
		memset(x2y2, 0, 64);
		if (x2_len <= 32)
			memcpy(x2y2 + 32 - x2_len, x2_buf, x2_len);
		else
			memcpy(x2y2, x2_buf + x2_len - 32, 32);

		if (y2_len <= 32)
			memcpy(x2y2 + 64 - y2_len, y2_buf, y2_len);
		else
			memcpy(x2y2 + 32, y2_buf + y2_len - 32, 32);

		kfree(x2_buf);
		kfree(y2_buf);

		/* 使用KDF生成密钥流t */
		rc = sm2_kdf(x2y2, 64, t, plaintext_len);
		if (rc)
			goto free_all;

		/* 检查t是否全零 */
		
		for (i = 0; i < plaintext_len; i++) {
			if (t[i] != 0) {
				all_zero = false;
				break;
			}
		}

		if (!all_zero)
			break;

	} while (--attempts > 0);

	if (attempts == 0) {
		rc = -EAGAIN;
		goto free_all;
	}

	/* 计算C2 = M ⊕ t */
	
	for (i = 0; i < plaintext_len; i++)
		c2[i] = plaintext[i] ^ t[i];

	/* 计算C3 = Hash(x2 || M || y2) */
	sm3_base_init(desc);
	crypto_sm3_update(desc, x2y2, 32);      /* x2 */
	crypto_sm3_update(desc, plaintext, plaintext_len);  /* M */
	crypto_sm3_update(desc, x2y2 + 32, 32); /* y2 */
	crypto_sm3_final(desc, c3);

	/* 组装密文 C = C1 || C3 || C2 */
	/* C1点的非压缩表示：04 || x1 || y1 */
	ciphertext[0] = 0x04;  /* 非压缩点标志 */

	

	c1x_buf = mpi_get_buffer(c1->x, &c1x_len, NULL);
	c1y_buf = mpi_get_buffer(c1->y, &c1y_len, NULL);

	if (!c1x_buf || !c1y_buf) {
		kfree(c1x_buf);
		kfree(c1y_buf);
		rc = -ENOMEM;
		goto free_all;
	}

	/* 填充C1的x坐标 */
	memset(ciphertext + 1, 0, 32);
	if (c1x_len <= 32)
		memcpy(ciphertext + 1 + 32 - c1x_len, c1x_buf, c1x_len);
	else
		memcpy(ciphertext + 1, c1x_buf + c1x_len - 32, 32);

	/* 填充C1的y坐标 */
	memset(ciphertext + 33, 0, 32);
	if (c1y_len <= 32)
		memcpy(ciphertext + 33 + 32 - c1y_len, c1y_buf, c1y_len);
	else
		memcpy(ciphertext + 33, c1y_buf + c1y_len - 32, 32);

	kfree(c1x_buf);
	kfree(c1y_buf);

	/* 填充C3 */
	memcpy(ciphertext + 65, c3, 32);

	/* C2已经在前面计算并填充 */

	*ciphertext_len = required_len;
	rc = 0;

free_all:
	mpi_free(k);
	mpi_point_release(c1);
	mpi_point_release(kpb);
	kfree(t);

	/* 清除敏感数据 */
	memzero_explicit(x2y2, sizeof(x2y2));
	memzero_explicit(c3, sizeof(c3));
	memzero_explicit(random_bytes, sizeof(random_bytes));

	return rc;
}
EXPORT_SYMBOL(sm2_encrypt_data);

/* strongSwan兼容的SM2解密函数 */
int sm2_decrypt_data(struct mpi_ec_ctx *ec, MPI private_key,
		     const unsigned char *ciphertext, size_t ciphertext_len,
		     unsigned char *plaintext, size_t *plaintext_len)
{
	MPI_POINT c1, dbc1;
	u8 x2y2[64];  /* x2 || y2 */
	u8 *t;        /* 密钥流 */
	u8 c3_computed[SM3_DIGEST_SIZE];
	SHASH_DESC_ON_STACK(desc, NULL);
	MPI x, y;
	size_t msg_len;
	int rc, i;
	/* 提取x2和y2的字节表示 */
	unsigned char *x2_buf, *y2_buf;
	unsigned int x2_len, y2_len;
	// 创建非const的临时变量
	u8 temp_buf[32];
	bool all_zero = true;
	const u8 *c2 = ciphertext + 97;  /* C2在密文中的位置 */
	/* 检查密文最小长度：1 + 64 + 32 = 97字节 */
	if (ciphertext_len < 97)
		return -EINVAL;

	/* 检查点压缩标志 */
	if (ciphertext[0] != 0x04)
		return -EINVAL;

	msg_len = ciphertext_len - 97;
	if (*plaintext_len < msg_len) {
		*plaintext_len = msg_len;
		return -ENOBUFS;
	}

	/* 分配变量 */
	c1 = mpi_point_new(0);
	dbc1 = mpi_point_new(0);
	x = mpi_new(256);
	y = mpi_new(256);
	t = kmalloc(msg_len, GFP_KERNEL);

	if (!c1 || !dbc1 || !x || !y || !t) {
		rc = -ENOMEM;
		goto free_all;
	}

	/* 解析C1点 */
	//rc = mpi_read_buffer(x, ciphertext + 1, 32, NULL, NULL);
	memcpy(temp_buf, ciphertext + 1, 32);
	rc = mpi_read_buffer(x, temp_buf, 32, NULL, NULL);
	if (rc)
		goto free_all;

	//rc = mpi_read_buffer(y, ciphertext + 33, 32, NULL, NULL);
	memcpy(temp_buf, ciphertext + 33, 32);
	rc = mpi_read_buffer(y, temp_buf, 32, NULL, NULL);
	if (rc)
		goto free_all;

	mpi_set(c1->x, x);
	mpi_set(c1->y, y);
	mpi_set_ui(c1->z, 1);

	/* 验证C1是否在椭圆曲线上 */
	if (!sm2_point_is_valid(ec, c1)) {
		rc = -EINVAL;
		goto free_all;
	}

	/* 计算S = h * C1，验证S不是无穷远点（h=1对SM2） */
	/* 对于SM2曲线，h=1，所以S=C1，只需验证C1有效即可 */

	/* 计算dB * C1 = (x2, y2) */
	mpi_ec_mul_point(dbc1, private_key, c1, ec);

	/* 获取仿射坐标 */
	rc = mpi_ec_get_affine(x, y, dbc1, ec);
	if (rc)
		goto free_all;

	

	x2_buf = mpi_get_buffer(x, &x2_len, NULL);
	y2_buf = mpi_get_buffer(y, &y2_len, NULL);

	if (!x2_buf || !y2_buf) {
		kfree(x2_buf);
		kfree(y2_buf);
		rc = -ENOMEM;
		goto free_all;
	}

	/* 将x2和y2拷贝到x2y2数组中 */
	memset(x2y2, 0, 64);
	if (x2_len <= 32)
		memcpy(x2y2 + 32 - x2_len, x2_buf, x2_len);
	else
		memcpy(x2y2, x2_buf + x2_len - 32, 32);

	if (y2_len <= 32)
		memcpy(x2y2 + 64 - y2_len, y2_buf, y2_len);
	else
		memcpy(x2y2 + 32, y2_buf + y2_len - 32, 32);

	kfree(x2_buf);
	kfree(y2_buf);

	/* 使用KDF生成密钥流t */
	rc = sm2_kdf(x2y2, 64, t, msg_len);
	if (rc)
		goto free_all;

	/* 检查t是否全零 */
	
	for (i = 0; i < msg_len; i++) {
		if (t[i] != 0) {
			all_zero = false;
			break;
		}
	}

	if (all_zero) {
		rc = -EINVAL;
		goto free_all;
	}

	/* 计算M' = C2 ⊕ t */
	//const u8 *c2 = ciphertext + 97;  /* C2在密文中的位置 */
	for (i = 0; i < msg_len; i++)
		plaintext[i] = c2[i] ^ t[i];

	/* 验证C3 = Hash(x2 || M' || y2) */
	sm3_base_init(desc);
	crypto_sm3_update(desc, x2y2, 32);           /* x2 */
	crypto_sm3_update(desc, plaintext, msg_len); /* M' */
	crypto_sm3_update(desc, x2y2 + 32, 32);      /* y2 */
	crypto_sm3_final(desc, c3_computed);

	/* 比较计算的C3与密文中的C3 */
	if (memcmp(c3_computed, ciphertext + 65, 32) != 0) {
		rc = -EINVAL;
		goto free_all;
	}

	*plaintext_len = msg_len;
	rc = 0;

free_all:
	mpi_point_release(c1);
	mpi_point_release(dbc1);
	mpi_free(x);
	mpi_free(y);
	kfree(t);

	/* 清除敏感数据 */
	memzero_explicit(x2y2, sizeof(x2y2));
	memzero_explicit(c3_computed, sizeof(c3_computed));

	return rc;
}
EXPORT_SYMBOL(sm2_decrypt_data);

/* 释放SM2密钥对 */
void sm2_free_keypair(struct sm2_key_pair *keypair)
{
	if (keypair) {
		mpi_free(keypair->private_key);
		mpi_point_release(keypair->public_key);
		keypair->private_key = NULL;
		keypair->public_key = NULL;
	}
}
EXPORT_SYMBOL(sm2_free_keypair);

/* 释放SM2签名 */
void sm2_free_signature(struct sm2_signature *signature)
{
	if (signature) {
		mpi_free(signature->r);
		mpi_free(signature->s);
		signature->r = NULL;
		signature->s = NULL;
	}
}
EXPORT_SYMBOL(sm2_free_signature);

/* 原始的SM2验证函数（保持兼容性） */
static int sm2_verify(struct akcipher_request *req)
{
	struct crypto_akcipher *tfm = crypto_akcipher_reqtfm(req);
	struct mpi_ec_ctx *ec = akcipher_tfm_ctx(tfm);
	unsigned char *buffer;
	struct sm2_signature_ctx sig;
	MPI hash;
	int ret;

	if (unlikely(!ec->Q))
		return -EINVAL;

	buffer = kmalloc(req->src_len + req->dst_len, GFP_KERNEL);
	if (!buffer)
		return -ENOMEM;

	sg_pcopy_to_buffer(req->src,
		sg_nents_for_len(req->src, req->src_len + req->dst_len),
		buffer, req->src_len + req->dst_len, 0);

	sig.sig_r = NULL;
	sig.sig_s = NULL;
	ret = asn1_ber_decoder(&sm2signature_decoder, &sig,
				buffer, req->src_len);
	if (ret)
		goto error;

	ret = -ENOMEM;
	hash = mpi_read_raw_data(buffer + req->src_len, req->dst_len);
	if (!hash)
		goto error;

	ret = _sm2_verify(ec, hash, sig.sig_r, sig.sig_s);

	mpi_free(hash);
error:
	mpi_free(sig.sig_r);
	mpi_free(sig.sig_s);
	kfree(buffer);
	return ret;
}

/* 设置SM2公钥 */
static int sm2_set_pub_key(struct crypto_akcipher *tfm,
			const void *key, unsigned int keylen)
{
	struct mpi_ec_ctx *ec = akcipher_tfm_ctx(tfm);
	MPI a;
	int rc;

	/* 包含未压缩标志'0x04' */
	a = mpi_read_raw_data(key, keylen);
	if (!a)
		return -ENOMEM;

	mpi_normalize(a);
	rc = sm2_ecc_os2ec(ec->Q, a);
	mpi_free(a);

	return rc;
}

/* 返回最大签名大小 */
static unsigned int sm2_max_size(struct crypto_akcipher *tfm)
{
	/* 无限制的最大大小 */
	return PAGE_SIZE;
}

/* 初始化变换函数 */
static int sm2_init_tfm(struct crypto_akcipher *tfm)
{
	struct mpi_ec_ctx *ec = akcipher_tfm_ctx(tfm);

	return sm2_ec_ctx_init(ec);
}

/* 退出变换函数 */
static void sm2_exit_tfm(struct crypto_akcipher *tfm)
{
	struct mpi_ec_ctx *ec = akcipher_tfm_ctx(tfm);

	sm2_ec_ctx_deinit(ec);
}

/* SM2算法结构定义 */
static struct akcipher_alg sm2 = {
	.verify = sm2_verify,
	.set_pub_key = sm2_set_pub_key,
	.max_size = sm2_max_size,
	.init = sm2_init_tfm,
	.exit = sm2_exit_tfm,
	.base = {
		.cra_name = "sm2",
		.cra_driver_name = "sm2-generic",
		.cra_priority = 100,
		.cra_module = THIS_MODULE,
		.cra_ctxsize = sizeof(struct mpi_ec_ctx),
	},
};

/* 模块初始化函数 */
static int sm2_init(void)
{
	return crypto_register_akcipher(&sm2);
}

/* 模块退出函数 */
static void sm2_exit(void)
{
	crypto_unregister_akcipher(&sm2);
}

subsys_initcall(sm2_init);
module_exit(sm2_exit);

MODULE_LICENSE("GPL");
MODULE_AUTHOR("Nathan Gao <gmy2171@163>");
MODULE_DESCRIPTION("SM2 通用算法完整实现，支持strongSwan");
MODULE_ALIAS_CRYPTO("sm2-generic");