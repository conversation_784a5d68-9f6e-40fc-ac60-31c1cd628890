/*
 * Copyright (C) 2009 <PERSON>
 * Copyright (C) 2008 <PERSON>
 * HSR Hochschule fuer Technik Rapperswil
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License as published by the
 * Free Software Foundation; either version 2 of the License, or (at your
 * option) any later version.  See <http://www.fsf.org/copyleft/gpl.txt>.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
 * for more details.
 */

#include "gmalg.h"

#include "gmalg_util.h"

#include <utils/debug.h>

#define PRI_TEMPLET_LEN 121
#define PRI_TEMPLET_PRI_OFFSET 7
#define PRI_TEMPLET_PUB_OFFSET_X 57
#define PRI_TEMPLET_PUB_OFFSET_Y 89

#define PUB_TEMPLET_LEN 91
#define PUB_TEMPLET_PUB_OFFSET_X 27
#define PUB_TEMPLET_PUB_OFFSET_Y 59

u_char pri_templet[] = {
	0x30 ,0x77 ,0x02 ,0x01 ,0x01 ,0x04 ,0x20 ,0xff  ,0x0f ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff,
	0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff  ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff,
	0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xa0  ,0x0a ,0x06 ,0x08 ,0x2a ,0x81 ,0x1c ,0xcf ,0x55,
	0x01 ,0x82 ,0x2d ,0xa1 ,0x44 ,0x03 ,0x42 ,0x00  ,0x04 ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff,
	0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff  ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff,
	0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff  ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff,
	0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff  ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff,
	0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff  ,0xff
};

u_char pub_templet[] = {
	0x30 ,0x59 ,0x30 ,0x13 ,0x06 ,0x07 ,0x2a ,0x86  ,0x48 ,0xce ,0x3d ,0x02 ,0x01 ,0x06 ,0x08 ,0x2a,
	0x81 ,0x1c ,0xcf ,0x55 ,0x01 ,0x82 ,0x2d ,0x03  ,0x42 ,0x00 ,0x04 ,0xff ,0xff ,0xff ,0xff ,0xff,
	0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff  ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff,
	0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff  ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff,
	0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff  ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff,
	0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff ,0xff  ,0xff ,0xff ,0xff
};

/* id default,签名预处理使用 */
u_char id_default[16] = {
	0x31, 0x32,0x33,0x34,0x35,0x36,0x37,0x38,
	0x31, 0x32,0x33,0x34,0x35,0x36,0x37,0x38,
};

/**
 * Described in header.
 */
bool gmalg_d2i_ec_pubkey(ECCrefPublicKey *pubkey, chunk_t data)
{
	if(data.len != PUB_TEMPLET_LEN)
		return FALSE;

	pubkey->bits = ntohl(0x100);
	memcpy(pubkey->x, data.ptr + PUB_TEMPLET_PUB_OFFSET_X, 32);
	memcpy(pubkey->y, data.ptr + PUB_TEMPLET_PUB_OFFSET_Y, 32);

	return TRUE;
}

/**
 * Described in header.
 */
bool gmalg_i2d_ec_pubkey(ECCrefPublicKey *pubkey, chunk_t *data)
{
	*data = chunk_alloc(PUB_TEMPLET_LEN);

	memcpy(data->ptr, pub_templet, PUB_TEMPLET_LEN);

	memcpy(data->ptr + PUB_TEMPLET_PUB_OFFSET_X, pubkey->x, 32);
	memcpy(data->ptr + PUB_TEMPLET_PUB_OFFSET_Y, pubkey->y, 32);

	return TRUE;
}

/**
 * Described in header.
 */
bool gmalg_d2i_ec_prikey(ECCrefPrivateKey *prikey, ECCrefPublicKey *pubkey, chunk_t data)
{

	if(data.len != PRI_TEMPLET_LEN)
		return FALSE;

	prikey->bits = ntohl(0x100);
	memcpy(prikey->K, data.ptr + PRI_TEMPLET_PRI_OFFSET, 32);

	pubkey->bits = ntohl(0x100);
	memcpy(pubkey->x, data.ptr + PRI_TEMPLET_PUB_OFFSET_X, 32);
	memcpy(pubkey->y, data.ptr + PRI_TEMPLET_PUB_OFFSET_Y, 32);

	return TRUE;
}

/**
 * Described in header.
 */
bool gmalg_i2d_EC_prikey(ECCrefPrivateKey *prikey, ECCrefPublicKey *pubkey,  chunk_t *data)
{

	*data = chunk_alloc(PRI_TEMPLET_LEN);
	memcpy(data->ptr, pri_templet, PRI_TEMPLET_LEN);

	memcpy(data->ptr + PRI_TEMPLET_PUB_OFFSET_X, pubkey->x, 32);
	memcpy(data->ptr + PRI_TEMPLET_PUB_OFFSET_Y, pubkey->y, 32);

	memcpy(data->ptr + PRI_TEMPLET_PRI_OFFSET, prikey->K, 32);

	return TRUE;
}
