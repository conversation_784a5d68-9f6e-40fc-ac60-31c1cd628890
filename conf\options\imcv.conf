charon {

    # Defaults for options in this section can be configured in the libimcv
    # section.
    imcv {

        # Whether IMVs send a standard IETF Assessment Result attribute.
        # assessment_result = yes

        # Global IMV policy database URI. If it contains a password, make sure
        # to adjust the permissions of the config file accordingly.
        # database =

        # Script called for each TNC connection to generate IMV policies.
        # policy_script = ipsec _imv_policy

        os_info {

            # Manually set whether a default password is enabled
            # default_password_enabled = no

            # Manually set the name of the client OS (e.g. Ubuntu).
            # name =

            # Manually set the version of the client OS (e.g. 12.04 i686).
            # version =

        }

    }

}

libimcv {

    # Debug level for a stand-alone libimcv library.
    # debug_level = 1

    # Plugins to load in IMC/IMVs with stand-alone libimcv library.
    # load = random nonce gmp pubkey x509

    # Disable output to stderr with a stand-alone libimcv library.
    # stderr_quiet = no

    swid_gen {

        # SWID generator command to be executed.
        # command = /usr/local/bin/swid_generator

        tag_creator {

            # Name of the tagCreator entity.
            # name = strongSwan Project

            # regid of the tagCreator entity.
            # regid = strongswan.org

        }

    }

}

