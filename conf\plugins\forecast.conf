forecast {

    # Multicast groups to join locally, allowing forwarding of them.
    # groups = 224.0.0.1,224.0.0.22,224.0.0.251,224.0.0.252,239.255.255.250

    # Local interface to listen for broadcasts to forward.
    # interface =

    # Whether to load the plugin. Can also be an integer to increase the
    # priority of this plugin.
    load = yes

    # CHILD_SA configurations names to perform multi/broadcast reinjection.
    # reinject =

}

