#ifndef __BASE_TYPE_DEF_H__
#define __BASE_TYPE_DEF_H__ 1

#ifdef _WIN32
#ifndef _WIN32_WINNT
#define _WIN32_WINNT 0x0501
#endif
//#define WIN32_LEAN_AND_MEAN
#include <windows.h>

typedef HANDLE	HDEV;

#else /* linux */
#include <stdbool.h>
#include <stddef.h>


#if defined(DRIVER_SDKEY) || defined(DRIVER_UKEY)
typedef int		HDEV;
#endif
typedef void*           HANDLE;

#define _GNU_SOURCE
#define __USE_GNU

typedef unsigned char     BYTE;
typedef char *            LPSTR;
typedef unsigned char *   PBYTE;
typedef int               INT_PTR;

typedef unsigned long      ULONG;
typedef unsigned long      DWORD;
typedef short              BOOL;

#endif

typedef unsigned char     u8;
typedef unsigned short    u16;
typedef unsigned int      u32;



#define NULL_PTR ((void *)0)

//#include <xchar.h>

#endif /* __BASE_TYPE_DEF_H__ */
