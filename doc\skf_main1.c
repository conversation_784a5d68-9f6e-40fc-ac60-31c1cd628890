#define __USE_GNU
#include "SKF.h"
#include <stdio.h>
#include <string.h>
#include <malloc.h>
#include <sys/ioctl.h>
#include <net/if.h>
#include <netinet/in.h>
#include <string.h>
#include "base_type.h"
#include "skf_type.h"
#define BYTE unsigned char
#define ULONG unsigned long
///////////////////////////////////////////////////////////////////////////////
// 多线程或多进程时使用SKF_LockDev和SKF_UnlockDev                            //
// 对不能中断的操作进行独占操作                                              //
// 比如需要使用外部认证后才能使用的函数，如CREATEAPP和DELETEAPP，            //
// 应该和外部认证函数一起进行独占，否则可能导致需要重新认证                  //
// INIT,UPDATE,FINAL之间也应该一起有独占                                     //
// 以下调用中只是示例，并不全面，需根据实际应用而定                          //
///////////////////////////////////////////////////////////////////////////////
typedef unsigned int u32;
typedef unsigned char u8;
#define LOG   printf

#define LOG_DATA(d, l)\
do\
{\
	int i;\
	for(i=0;i<l;i++)\
	{\
		if((i+1) % 16) \
			printf("%02X ", d[i]);\
		else\
			printf("%02X\n", d[i]);\
	}\
	if(i % 16) printf("\n");\
}\
while(0)
#include <pthread.h>
#define RET_PAUSE  {system("pause"); return ret;}

u32 Enum_App(DEVHANDLE* hDev, char** pszList)
{
	u32 len;
	char* szList;

	u32 r = SKF_EnumApplication(hDev, NULL, &len);
	if(r)
	{
		LOG("SKF_EnumApplication error 0x%x\n", r);
		return r;
	}

	szList = malloc(len);
	if(szList == NULL)
	{
		LOG("malloc error \n");
		return 1;
	}

	r = SKF_EnumApplication(hDev, szList, &len);
	if(r)
	{
		free(szList);
		LOG("SKF_EnumApplication error 0x%x\n", r);
		return r;
	}
	LOG("SKF_EnumApplication OK\n");
	*pszList = szList;
	return r;
}

u32 Enum_Cnt(HAPPLICATION* hApp, char** pszList)
{
	char* szList = NULL;
	u32 r, len;

	r = SKF_EnumContainer(hApp, NULL, &len);
	if(r)
	{
		LOG("SKF_EnumContainer error 0x%x\n", r);
		return r;
	}

	szList = malloc(len);
	if(szList == NULL)
	{
		LOG("malloc error \n");
		return 1;
	}

	r = SKF_EnumContainer(hApp, szList, &len);
	if(r)
	{
		free(szList);
		LOG("SKF_EnumContainer error 0x%x\n", r);
		return r;
	}
	LOG("SKF_EnumContainer OK\n");
	*pszList = szList;
	return 0;
}

u32 DevAuth(DEVHANDLE* hDev, char* szKey)
{
	u8 rand[16] = {0}, authdata[32] = {0};
	u32 ret, l = 16;
	char ss[12];
	char* szAlgo = NULL;
	DEVINFO info;
	ret = SKF_GetDevInfo(hDev, &info);
	if(ret)
	{
		LOG("SKF_GetDevInfo error 0x%x----%d\n",ret,__LINE__);
		return ret;
	}
	switch(info.DevAuthAlgId)
	{
	case SGD_SM1_ECB:
		szAlgo = "SGD_SM1_ECB";
		break;
	case SGD_SMS4_ECB:
		szAlgo = "SGD_SMS4_ECB";
		break;
	default:
		sprintf(ss, "0x%x", info.DevAuthAlgId);
		szAlgo = ss;
	}
	if(info.DevAuthAlgId == SGD_SM1_ECB || info.DevAuthAlgId == SGD_SMS4_ECB)
	{
		HANDLE hKey;
		BLOCKCIPHERPARAM bp;

		bp.IVLen = 0;
		bp.PaddingType = NO_PADDING;
		bp.FeedBitLen = 0;
		ret = SKF_GenRandom(hDev, rand, 8);
		if(ret)
		{
			LOG("SKF_GenRandom error 0x%x----%d\n",ret,__LINE__);
			return ret;
		}
		ret = SKF_SetSymmKey(hDev, (u8*)szKey, info.DevAuthAlgId, &hKey);
		if(ret)
		{
			LOG("SKF_SetSymmKey error 0x%x----%d\n",ret,__LINE__);
			SKF_CloseHandle(hKey);
			return ret;
		}
		ret = SKF_EncryptInit(hKey, bp);
		if(ret)
		{
			LOG("SKF_EncryptInit error 0x%x----%d\n",ret,__LINE__);
			SKF_CloseHandle(hKey);
			return ret;
		}
		ret = SKF_Encrypt(hKey, rand, 16, authdata, &l);
		SKF_CloseHandle(hKey);
		if(ret) 
		{
			LOG("SKF_Encrypt error 0x%x----%d\n",ret,__LINE__);
			return ret;
		}
	}
	else
	{
		LOG("Device algorithm UNKOWN!!!\n");
		return 1;
	}

	ret = SKF_DevAuth(hDev, authdata, l);
	if(ret)
	{
		LOG("SKF_DevAuth error 0x%x----%d\n",ret,__LINE__);
		return ret;
	}
	LOG("SKF_DevAuth OK\n");
	return ret;
}

u32 ChangeDevAuth(DEVHANDLE* hDev, char* szOldKey, char* szNewKey)
{
	u32 ret;
	ret = DevAuth(hDev, szOldKey);
	if(ret) 
	{
		return ret;
	}
	SKF_LockDev(hDev,-1);
	ret = SKF_ChangeDevAuthKey(hDev, (u8*)szNewKey, (u32)strlen(szNewKey));
	if(ret)
	{
		SKF_UnlockDev(hDev);
		LOG("SKF_ChangeDevAuthKey error 0x%x----%d\n",ret,__LINE__);
	}
	LOG("SKF_ChangeDevAuthKey OK\n");
	SKF_UnlockDev(hDev);
	return ret;
}
u32 Sym_Test(DEVHANDLE hDev, u32 algo)
{
	u32 len;
	u32 r, i = 0;
	BLOCKCIPHERPARAM bp;
	HANDLE hKey = NULL;

	u8 key[16] = {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15};
	u8 data[1024] = {0}, endata[1024], decdata[1024];

	memset(&bp, 0, sizeof(BLOCKCIPHERPARAM));
	bp.PaddingType = 1;
	bp.IVLen = 0;

	r = SKF_SetSymmKey(hDev, key, algo, &hKey);
	if(r)
	{
		LOG("SKF_SetSymmKey error 0x%x\n", r);
		goto err;
	}
	LOG("SKF_SetSymmKey OK\n");
	r = SKF_EncryptInit(hKey, bp);
	if(r)
	{
		LOG("SKF_EncryptInit error 0x%x\n", r);
		goto err;
	}
	LOG("SKF_EncryptInit OK\n");
	len = 1024;
	r = SKF_Encrypt(hKey, data, 796, endata, &len);
	if(r)
	{
		LOG("SKF_Encrypt error 0x%x\n", r);
		goto err;
	}
	LOG("SKF_Encrypt OK\n");
	SKF_CloseHandle(hKey);
	hKey = NULL;
	LOG("SKF_CloseHandle OK\n");
	r = SKF_SetSymmKey(hDev, key, algo, &hKey);
	if(r)
	{
		LOG("SKF_SetSymmKey error 0x%x\n", r);
		goto err;
	}
	LOG("SKF_SetSymmKey OK\n");
	r = SKF_DecryptInit(hKey, bp);
	if(r)
	{
		LOG("SKF_DecryptInit error 0x%x\n", r);
		goto err;
	}
	LOG("SKF_DecryptInit OK\n");
	r = SKF_Decrypt(hKey, endata, len, decdata, &len);
	if(r)
	{
		LOG("SKF_Decrypt error 0x%x\n", r);
		goto err;
	}
	LOG("SKF_Decrypt OK\n");
	SKF_CloseHandle(hKey);
	if(len != 796 || memcmp(decdata, data, len))
	{
		LOG("data  != decdata \n");
	}
	else
	{
		LOG("data  == decdata \n");
	}
	return 0;

err:
	if(hKey) SKF_CloseHandle(hKey);
	return 1;
}

u32 File_Test(HAPPLICATION hApp, char* szPin)
{
	u32 len,i;
	u32 r;
	u8 data[128], rdata[128];

	for(i=0;i<128;i++)
		data[i] = (u8)i;

	//FILE NOT SUPPORT SECURE_ADM_ACCOUNT
	r = SKF_CreateFile(hApp, "test1", 32, SECURE_ANYONE_ACCOUNT, SECURE_ANYONE_ACCOUNT);
	if(r)
	{
		LOG("SKF_CreateFile error 0x%x\n", r);
		return r;
	}
	LOG("SKF_CreateFile OK\n");
	
	r = SKF_WriteFile(hApp, "test1", 0, data, 12);
	if(r)
	{
		LOG("SKF_WriteFile error 0x%x\n", r);
		return r;
	}
	LOG("SKF_WriteFile OK\n");
	len = 128;
	r = SKF_ReadFile(hApp, "test1", 0, 12, rdata, &len);
	if(r)
	{
		LOG("SKF_ReadFile error 0x%x\n", r);
		return r;
	}
	LOG("SKF_ReadFile OK\n");
	
	r = SKF_DeleteFile(hApp, "test1");
	if(r)
	{
		LOG("SKF_DeleteFile error 0x%x\n", r);
		return r;
	}
	LOG("SKF_DeleteFile OK\n");
	return 0;
}
u32 OpenApp(DEVHANDLE hDev, char* szName, HAPPLICATION *phApp, char* szPin)
{
	HAPPLICATION hApp;
	char* szList = NULL;
	u32 r = 0, len;

	if(szName == NULL)
	{
		int i = 1,j;
		r = Enum_App(hDev, &szList);
		if(r) return r;

		szName = szList;
		if(szName && *szName)
			printf("-- Please select an application NUMBER to open\n");

		while(szName && *szName)
		{
			printf("%d %s\n", i++, szName);
			szName += strlen(szName) + 1;
		}

		scanf("%d", &i);

		szName = szList;
		for(j=1;j<i;j++)
		{
			szName += strlen(szName) + 1;
		}
	}
	
	r = SKF_OpenApplication(hDev, szName, &hApp);
	if(szList) free(szList);
	if(r)
	{
		LOG("SKF_OpenApplication error 0x%x\n", r);
		return 1;
	}

	r = SKF_VerifyPIN(hApp, USER_TYPE, szPin, &len);
	if(r)
	{
		SKF_CloseApplication(hApp);
		LOG("SKF_VerifyPIN error 0x%x, retry = %d \n", r, len);
		return 1;
	}
	*phApp = hApp;
	return 0;
}
int CreApp_Test(DEVHANDLE hDev,HAPPLICATION *hApp)
{
	u32 r;
	char szList[260] = {0}, *p;
	u32 uretry = 0,MaxRetry = 0;
	u8 authKey[16] = {0x43,0x2A,0x43,0x4F,0x52,0x45,0x20,0x53,0x59,0x53,0x20,0x40,0x20,0x53,0x5A,0x20};//{0xEE,0xED,0xF4,0xA7,0x48,0x50,0x4D,0x6F,0x88,0x59,0xB4,0x40,0x18,0x4D,0x85,0x1E};
	BOOL blDefault = TRUE;
	HAPPLICATION hAppEx;
	int len = 260;
	//char* szList;

	r = SKF_EnumApplication(hDev, szList, &len);
	if(r)
	{
			LOG("SKF_EnumApplication error 0x%x\n", r);
			return r;
	}
	LOG("SKF_EnumApplication OK\n");
	p = szList;

	r = DevAuth(hDev, authKey);//C*CORE SYS @ SZ 
	if(r) return r;

	r = SKF_CreateApplication(hDev, "CCORE", "111111", 6, "111111", 6, 0xFF, &hAppEx);
	if(r)
	{
		LOG("SKF_CreateApplication error 0x%x\n", r);
		return r;
	}
	LOG("SKF_CreateApplication OK\n");
	
	r = SKF_GetPINInfo(hAppEx,USER_TYPE,&MaxRetry,&uretry,&blDefault);
	if(r)
	{
		LOG("SKF_GetPINInfo error 0x%x\n", r);
		return r;
	}
	LOG("SKF_GetPINInfo OK\n");
	
	r = SKF_ChangePIN(hAppEx,USER_TYPE,"111111","123456",&uretry);
	if(r)
	{
		LOG("SKF_ChangePIN error 0x%x\n", r);
		return r;
	}
	LOG("SKF_ChangePIN OK\n");
	
	//r = SKF_UnblockPIN(hAppEx,"111111","111111",&uretry);
	if(r)
	{
		LOG("SKF_UnblockPIN error 0x%x\n", r);
		return r;
	}
	//2020.11.12
	LOG("SKF_UnblockPIN OK\n");
	r = SKF_ClearSecureState(hAppEx);
	if(r)
	{
		LOG("SSKF_ClearSecureState error 0x%x\n", r);
		return r;
	}
	LOG("SSKF_ClearSecureState OK\n");
	SKF_CloseApplication(hAppEx);
	LOG("SKF_CloseApplication OK\n");
	r = DevAuth(hDev, authKey);
	if(r) return r;

	r = SKF_DeleteApplication(hDev, "CCORE");
	if(r)
	{
			LOG("SKF_DeleteApplication error 0x%x\n", r);
			return r;
	}
	LOG("Delete Application  OK\n");
	
	r = DevAuth(hDev, authKey);//C*CORE SYS @ SZ 
	if(r) return r;

	r = SKF_CreateApplication(hDev, "TestSKF", "12345678", 6, "111111", 6, 0xFF, &hAppEx);
	if(r)
	{
		if(0x0A00002F != r)
		{
			LOG("SKF_CreateApplication error 0x%x\n", r);
			return r;
		}
		r = SKF_OpenApplication(hDev,"TestSKF",&hAppEx);
		if(r)
		{
			LOG("SKF_OpenApplication error 0x%x\n", r);
			return r;
		}
		LOG("SKF_OpenApplication OK\n");
	}
	else
	{
		LOG("SKF_CreateApplication OK\n");
	}
	*hApp = hAppEx;
	return r;
}

u32 RSA_ImportKeyAndCert(DEVHANDLE hDev, HCONTAINER hCont)
{
	u8 keypair[] = {
		//modulus
		0xd4,0x02,0xde,0x00,0x43,0xbb,0x6c,0x89,0x07,0x30,0x56,0x18,0x9b,0x1e,0x7d,
		0xb6,0x3e,0x57,0x0c,0x70,0x88,0x5d,0xdb,0xdf,0x84,0x1d,0x78,0x20,0xa5,0x82,
		0xfb,0x68,0xbb,0xde,0xe0,0x44,0xd9,0x89,0x8d,0xed,0x74,0x32,0x10,0x00,0xe5,
		0x63,0x2e,0x8e,0x0a,0xeb,0xed,0xc6,0x55,0x78,0xef,0xe6,0x8a,0xcb,0xf9,0xf8,
		0x50,0x5d,0xe7,0xbb,0xc2,0x99,0xf4,0xb7,0x82,0x72,0xfa,0xc3,0xf7,0x6c,0x61,
		0xf8,0x8c,0x9d,0x81,0x57,0x89,0xc6,0xb9,0xa2,0xd2,0x82,0x1c,0x3d,0x25,0xb9,
		0x44,0xc9,0x82,0x8c,0x4b,0xe2,0x9c,0xd5,0x5a,0x59,0xa7,0xa6,0xfa,0xa5,0xfa,
		0x08,0x7a,0x58,0xa4,0x06,0x11,0xc3,0xa2,0x7f,0xc6,0x61,0x33,0xe0,0xc2,0x8a,
		0x0e,0xe4,0x23,0x5c,0xbb,0x84,0x5f,0x8b,
		//p
		0xfe,0x0e,0x81,0xb0,0xc3,0x30,0xb6,0x18,0xa5,0x53,0x5f,0xeb,0x08,0xde,
		0x82,0xb4,0x57,0xf3,0xae,0x2c,0x7d,0x70,0x76,0xe5,0x37,0xc0,0xe7,0xf9,0xe6,
		0x0d,0xa0,0xc6,0x74,0x6e,0xbe,0x75,0x70,0x96,0x83,0x3e,0x17,0x79,0x1a,0x6c,
		0x2d,0xe2,0xf2,0x10,0x95,0xd2,0x79,0x81,0xea,0x76,0xbe,0xe4,0x00,0x9d,0x14,
		0xa4,0xa0,0xb0,0xf7,0x15,
		//q
		0xd5,0xa2,0x06,0xf7,0xa9,0xb8,0xed,0xd7,0x1d,0xd8,0x0e,0xac,0xc6,0x61,
		0x94,0x54,0xbb,0x35,0xb1,0x15,0xfc,0xed,0x8f,0xf3,0xbf,0xe1,0xb4,0xd5,0xde,
		0x04,0xcc,0xfc,0x89,0x7d,0xe2,0x5e,0x32,0x96,0x52,0xb2,0x50,0x9f,0x4b,0x00,
		0x37,0x64,0x7a,0x07,0x00,0x62,0x75,0xb6,0x2f,0x4b,0xf2,0xcd,0xc1,0x6d,0x3d,
		0x8e,0x5b,0xd9,0xa4,0x1f,
		//dp
		0x2e,0x0c,0xa5,0x17,0x4c,0x19,0xfd,0x37,0xb4,0x67,0xcb,0x60,0x07,0xc8,0x85,
		0x3a,0x79,0x22,0xb3,0x34,0x5f,0x3c,0x4e,0x60,0xb7,0xdd,0x60,0x6e,0xdc,0x73,
		0x25,0xec,0x32,0xd8,0x8b,0xef,0x2f,0x8c,0x28,0x97,0xcd,0x9b,0x66,0xdd,0xaa,
		0xe7,0x92,0xe6,0xcc,0xb1,0x4c,0xd2,0xee,0x93,0xbd,0x80,0x08,0x58,0x70,0x90,
		0x72,0x8c,0x01,0x0d,
		//dq
		0x70,0x01,0x1b,0x16,0x0d,0xed,0xdf,0x04,0xc1,0xa8,0xdd,0x48,0xc8,0x59,0xb0,
		0xa3,0x1b,0xe3,0xf2,0x8c,0x4c,0xa7,0x60,0xa9,0xb3,0x18,0x6a,0xef,0x16,0x0f,
		0xfe,0x49,0x08,0xec,0xef,0x19,0xe3,0xfb,0xdc,0x2f,0x91,0x05,0x73,0x15,0x11,
		0xf5,0xa3,0xe4,0xb7,0xd6,0xe7,0x50,0x35,0x4b,0xe4,0x68,0xeb,0xd8,0x92,0x45,
		0x68,0xf2,0x9a,0x91,
		//coef
		0xb1,0x04,0x4a,0x7e,0x1d,0x2b,0x45,0xbb,0xa6,0x87,0x2f,0x4b,0x28,0xf5,
		0xaf,0x51,0x55,0xe5,0x6b,0x99,0x27,0x53,0x86,0xed,0x00,0x76,0x22,0x98,0xcd,
		0x8b,0xa5,0xa2,0xc8,0x1a,0xf8,0xa1,0x30,0xb9,0x97,0x72,0x0f,0x74,0xf5,0xdb,
		0x43,0x04,0x65,0x71,0x1e,0x95,0xb8,0x3e,0x62,0xee,0x0d,0xc0,0x31,0x3e,0xc5,
		0xc9,0xb0,0x0f,0xec,0x93,
		//d
		0x06,0x74,0xff,0xe5,0xea,0x2a,0x2f,0x28,0x0d,0x3b,0xff,0xa3,0x48,0x36,0x29,
		0x85,0xff,0x68,0x0f,0x4c,0xee,0x1a,0x4d,0xb4,0x05,0x06,0x10,0xbe,0x8d,0xcb,
		0xeb,0x74,0x49,0x24,0x84,0x98,0x14,0x38,0x08,0x46,0x8f,0x1f,0x67,0x35,0x5f,
		0xc7,0x21,0x87,0xd9,0xf5,0x4d,0x8c,0x98,0xd5,0xd2,0x18,0xa7,0x69,0x86,0xf7,
		0x75,0x1b,0x27,0x0f,0x96,0xa3,0x2d,0x53,0x58,0x48,0x92,0x88,0x6b,0xfe,0xfe,
		0x6a,0x97,0xd9,0xdc,0x7e,0xff,0x45,0xd3,0x96,0xd3,0x98,0x65,0xbe,0x63,0x58,
		0xf5,0x4c,0xb9,0xec,0x64,0x43,0x7a,0x47,0x84,0x3e,0xfc,0xab,0xeb,0xaa,0x4c,
		0x03,0x8f,0xdf,0x92,0x19,0xa4,0x5a,0x15,0x15,0xc2,0x6f,0xd2,0x07,0xd0,0x63,
		0x86,0x13,0xac,0x37,0x25,0x45,0xde,0x21
	};
u8 cert[] = {
	0x30, 0x82, 0x02, 0x9A, 0x30, 0x82, 0x01, 0x82, 0xA0, 0x03, 0x02, 0x01, 0x02, 0x02, 0x0A, 0x1A,
	0x56, 0xC6, 0x83, 0x00, 0x00, 0x00, 0x00, 0x01, 0xDD, 0x30, 0x0D, 0x06, 0x09, 0x2A, 0x86, 0x48,
	0x86, 0xF7, 0x0D, 0x01, 0x01, 0x05, 0x05, 0x00, 0x30, 0x12, 0x31, 0x10, 0x30, 0x0E, 0x06, 0x03,
	0x55, 0x04, 0x03, 0x13, 0x07, 0x43, 0x31, 0x31, 0x30, 0x36, 0x43, 0x41, 0x30, 0x1E, 0x17, 0x0D,
	0x31, 0x33, 0x31, 0x31, 0x30, 0x37, 0x30, 0x30, 0x35, 0x39, 0x35, 0x39, 0x5A, 0x17, 0x0D, 0x31,
	0x34, 0x31, 0x31, 0x30, 0x37, 0x30, 0x31, 0x30, 0x39, 0x35, 0x39, 0x5A, 0x30, 0x21, 0x31, 0x0B,
	0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x43, 0x4E, 0x31, 0x12, 0x30, 0x10, 0x06,
	0x03, 0x55, 0x04, 0x03, 0x13, 0x09, 0x73, 0x69, 0x67, 0x6E, 0x61, 0x74, 0x75, 0x72, 0x65, 0x30,
	0x81, 0x9F, 0x30, 0x0D, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01, 0x05,
	0x00, 0x03, 0x81, 0x8D, 0x00, 0x30, 0x81, 0x89, 0x02, 0x81, 0x81, 0x00, 0xD4, 0x02, 0xDE, 0x00,
	0x43, 0xBB, 0x6C, 0x89, 0x07, 0x30, 0x56, 0x18, 0x9B, 0x1E, 0x7D, 0xB6, 0x3E, 0x57, 0x0C, 0x70,
	0x88, 0x5D, 0xDB, 0xDF, 0x84, 0x1D, 0x78, 0x20, 0xA5, 0x82, 0xFB, 0x68, 0xBB, 0xDE, 0xE0, 0x44,
	0xD9, 0x89, 0x8D, 0xED, 0x74, 0x32, 0x10, 0x00, 0xE5, 0x63, 0x2E, 0x8E, 0x0A, 0xEB, 0xED, 0xC6,
	0x55, 0x78, 0xEF, 0xE6, 0x8A, 0xCB, 0xF9, 0xF8, 0x50, 0x5D, 0xE7, 0xBB, 0xC2, 0x99, 0xF4, 0xB7,
	0x82, 0x72, 0xFA, 0xC3, 0xF7, 0x6C, 0x61, 0xF8, 0x8C, 0x9D, 0x81, 0x57, 0x89, 0xC6, 0xB9, 0xA2,
	0xD2, 0x82, 0x1C, 0x3D, 0x25, 0xB9, 0x44, 0xC9, 0x82, 0x8C, 0x4B, 0xE2, 0x9C, 0xD5, 0x5A, 0x59,
	0xA7, 0xA6, 0xFA, 0xA5, 0xFA, 0x08, 0x7A, 0x58, 0xA4, 0x06, 0x11, 0xC3, 0xA2, 0x7F, 0xC6, 0x61,
	0x33, 0xE0, 0xC2, 0x8A, 0x0E, 0xE4, 0x23, 0x5C, 0xBB, 0x84, 0x5F, 0x8B, 0x02, 0x03, 0x01, 0x00,
	0x01, 0xA3, 0x67, 0x30, 0x65, 0x30, 0x0E, 0x06, 0x03, 0x55, 0x1D, 0x0F, 0x01, 0x01, 0xFF, 0x04,
	0x04, 0x03, 0x02, 0x06, 0xC0, 0x30, 0x1D, 0x06, 0x03, 0x55, 0x1D, 0x0E, 0x04, 0x16, 0x04, 0x14,
	0xAC, 0x44, 0x27, 0xD2, 0x29, 0xC4, 0x7A, 0xC4, 0x3A, 0xBD, 0x75, 0xA8, 0x5E, 0x76, 0xEF, 0xFA,
	0x5E, 0xF2, 0x84, 0x63, 0x30, 0x13, 0x06, 0x03, 0x55, 0x1D, 0x25, 0x04, 0x0C, 0x30, 0x0A, 0x06,
	0x08, 0x2B, 0x06, 0x01, 0x05, 0x05, 0x07, 0x03, 0x02, 0x30, 0x1F, 0x06, 0x03, 0x55, 0x1D, 0x23,
	0x04, 0x18, 0x30, 0x16, 0x80, 0x14, 0xD5, 0xBB, 0x75, 0x63, 0xEE, 0x81, 0x18, 0x5E, 0xE4, 0x09,
	0xBA, 0xD3, 0x60, 0x27, 0xB8, 0x1A, 0x04, 0xDA, 0xD3, 0x18, 0x30, 0x0D, 0x06, 0x09, 0x2A, 0x86,
	0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x05, 0x05, 0x00, 0x03, 0x82, 0x01, 0x01, 0x00, 0x4E, 0x31,
	0xB3, 0x75, 0xA7, 0x3B, 0x77, 0x60, 0xF9, 0xC6, 0x53, 0xAC, 0xE5, 0x0F, 0xB9, 0xE6, 0xD6, 0xF1,
	0x8A, 0x43, 0xEA, 0x30, 0x56, 0x37, 0x85, 0x83, 0x42, 0x71, 0xD2, 0x76, 0xB2, 0x14, 0x2B, 0x8F,
	0x73, 0x3F, 0x92, 0x2F, 0xF7, 0xE8, 0xC3, 0xE6, 0x39, 0x95, 0x2C, 0x48, 0x02, 0xDB, 0x4B, 0x5D,
	0x7D, 0x28, 0x38, 0xC9, 0x89, 0xDE, 0x72, 0xCB, 0xCF, 0xA6, 0x3D, 0x1F, 0x8D, 0x31, 0x22, 0x2F,
	0xD8, 0x40, 0x99, 0x00, 0xF8, 0xD3, 0xA7, 0xD8, 0xD0, 0x35, 0x68, 0xCB, 0x92, 0x9B, 0x1C, 0x1B,
	0xF7, 0x49, 0x0B, 0x4A, 0x38, 0x8C, 0xA8, 0x63, 0x16, 0xEC, 0x3A, 0xE1, 0x76, 0x54, 0x06, 0x5C,
	0x9E, 0x06, 0xE4, 0x97, 0x00, 0x01, 0x7B, 0x16, 0xE0, 0x17, 0xFE, 0x17, 0x8E, 0xD1, 0xB8, 0x53,
	0x6A, 0x33, 0xA9, 0xCE, 0x65, 0xFF, 0x2A, 0x08, 0xCC, 0x83, 0xEE, 0x13, 0x56, 0x9E, 0xB2, 0xA4,
	0x72, 0x48, 0x6F, 0x87, 0x82, 0x68, 0x95, 0xAD, 0x20, 0xAA, 0x63, 0xF2, 0xAA, 0x8B, 0x60, 0xC2,
	0x65, 0xC4, 0x93, 0xF0, 0x7A, 0xED, 0xDE, 0xE4, 0x01, 0xD5, 0xDA, 0xDC, 0xA4, 0x13, 0x85, 0xD2,
	0xA2, 0xDD, 0x14, 0x17, 0xBE, 0x5E, 0x6B, 0x82, 0x30, 0xCF, 0xF1, 0x92, 0xD0, 0x5F, 0x16, 0x0F,
	0xBE, 0x2E, 0x38, 0x57, 0x67, 0xA6, 0x57, 0x73, 0x48, 0x9D, 0x51, 0xF0, 0x1A, 0x48, 0x36, 0x21,
	0x3C, 0x30, 0x2D, 0xA7, 0xA6, 0x95, 0x84, 0xC9, 0xA5, 0x3A, 0xC3, 0x91, 0xF7, 0x07, 0x26, 0x8B,
	0x54, 0x35, 0x54, 0xF1, 0x2A, 0xD8, 0xC9, 0xA5, 0x72, 0x58, 0xC8, 0xD4, 0x04, 0x03, 0x0E, 0x49,
	0xF0, 0xF6, 0xC2, 0xEC, 0x02, 0x7F, 0x55, 0xE4, 0x2B, 0x2A, 0x14, 0xDB, 0x96, 0x2F, 0x2A, 0x48,
	0xA6, 0xFE, 0x85, 0xEC, 0xB4, 0xA5, 0x52, 0x69, 0x3A, 0x48, 0x23, 0x8A, 0x67, 0x5E};

	u8 certEx[1024] = {0};
	u32 certExLen = 1024;
	u32 ret, len = 128, wapperlen;
	u8 pbWappData[256];
	RSAPUBLICKEYBLOB pub;
	HANDLE hKey;
	BLOCKCIPHERPARAM bp;
	u8 encryptkey[1024];

	bp.PaddingType = 1;
	bp.IVLen = 0;
	bp.FeedBitLen = 0;

	len = 1024;

	len = sizeof(RSAPUBLICKEYBLOB);
	ret = SKF_ExportPublicKey(hCont, 1, (u8*)&pub, &len);
	if(ret) 
	{
		if(ret == SAR_KEYNOTFOUNTERR)
		{
			ret = SKF_GenRSAKeyPair(hCont, 1024, &pub);
			if(ret) 
			{
				LOG("SKF_GenRSAKeyPair error : 0x%x----%d\n",(u32)ret,__LINE__);
				return ret;
			}
			LOG("SKF_GenRSAKeyPair OK \n");
		}
		else
		{
			LOG("SKF_ExportPublicKey error : 0x%x----%d\n",(u32)ret,__LINE__);
			return ret;
		}
	}
	LOG("SKF_ExportPublicKey OK \n");
	wapperlen = 256;
	ret = SKF_RSAExportSessionKey(hCont, SGD_SM1_ECB, &pub, pbWappData, (u32*)&wapperlen, &hKey);
	if(ret)
	{
		LOG("SKF_RSAExportSessionKey error : 0x%x----%d\n",(u32)ret,__LINE__);
		return ret;
	}
	LOG("SKF_RSAExportSessionKey OK \n");
	
	ret = SKF_EncryptInit(hKey, bp);
	if(ret)
	{
		LOG("SKF_EncryptInit error : 0x%x----%d\n",(u32)ret,__LINE__);
		return ret;
	}
	LOG("SKF_EncryptInit OK \n");
	len = 1024;
	ret = SKF_Encrypt(hKey, keypair, sizeof(keypair), encryptkey, &len);
	if(ret)
	{
		LOG("SKF_Encrypt error : 0x%x----%d\n",(u32)ret,__LINE__);
		return ret;
	}
	LOG("SKF_Encrypt OK \n");
	
	ret = SKF_ImportRSAKeyPair(hCont, SGD_SM1_ECB, pbWappData, wapperlen, encryptkey, len);
	if(ret)
	{
		LOG("SKF_ImportRSAKeyPair error : 0x%x----%d\n",(u32)ret,__LINE__);
		return ret;
	}
	LOG("SKF_ImportRSAKeyPair OK \n");
	ret = SKF_ImportCertificate(hCont, 0, cert, sizeof(cert));
	if(ret)
	{
		LOG("SKF_ImportCertificate error : 0x%x----%d\n",(u32)ret,__LINE__);
		return ret;
	}
	LOG("SKF_ImportCertificate OK \n");
	ret = SKF_ExportCertificate(hCont, 0, certEx, &certExLen);
	if(ret)
	{
		LOG("SKF_ExportCertificate error : 0x%x----%d\n",(u32)ret,__LINE__);
		return ret;
	}
	LOG("SKF_ExportCertificate OK \n");
	return 0;
}

u32 ECC_ImportKeyAndCert(DEVHANDLE hDev, HCONTAINER hCont)
{
	u8 keypair[] = {
		//x
		0x19, 0x79, 0x5d, 0xf7, 0x01, 0xf3, 0x9d, 0x1f, 0xb2, 0x20, 0xc4, 0x5f, 0xa7, 0xfa, 0x4e, 0xbf, 
		0xad, 0xd1, 0x70, 0x25, 0x37, 0xb9, 0x46, 0xcd, 0x3d, 0x48, 0x04, 0xb3, 0x7f, 0xbc, 0x3e, 0xa5,
		//y
		0x2b, 0x2c, 0xee, 0xd6, 0xcc, 0x04, 0x2b, 0x5b, 0xbb, 0x56, 0x8d, 0xed, 0x3b, 0x36, 0x73, 0xf2, 
		0x88, 0xe1, 0x9c, 0xc4, 0x9a, 0xe3, 0xc3, 0x50, 0xd2, 0xb8, 0x09, 0x03, 0xd8, 0x6d, 0x91, 0x2c,
		//d
		0x3f, 0x91, 0x68, 0xe8, 0x6d, 0x2a, 0xac, 0xaa, 0x2c, 0x81, 0xd8, 0xba, 0x24, 0x9b, 0xc9, 0x5a, 
		0x60, 0xe0, 0x47, 0x50, 0xa2, 0xee, 0xaa, 0x63, 0x26, 0x2b, 0x54, 0xc4, 0x75, 0x51, 0xb8, 0xdc
	};

	u8 cert[] = {
		0x30, 0x82, 0x03, 0x01, 0x30, 0x82, 0x02, 0xA7, 0xA0, 0x03, 0x02, 0x01, 0x02, 0x02, 0x08, 0x12,
		0x34, 0x56, 0x78, 0x9A, 0xBC, 0xDE, 0xF1, 0x30, 0x0A, 0x06, 0x08, 0x2A, 0x81, 0x1C, 0xCF, 0x55,
		0x01, 0x83, 0x75, 0x30, 0x7D, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02,
		0x43, 0x4E, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x08, 0x13, 0x02, 0x47, 0x44, 0x31,
		0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x07, 0x13, 0x02, 0x53, 0x5A, 0x31, 0x10, 0x30, 0x0E,
		0x06, 0x03, 0x55, 0x04, 0x0A, 0x13, 0x07, 0x53, 0x41, 0x4E, 0x47, 0x46, 0x4F, 0x52, 0x31, 0x0C,
		0x30, 0x0A, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x13, 0x03, 0x53, 0x53, 0x4C, 0x31, 0x10, 0x30, 0x0E,
		0x06, 0x03, 0x55, 0x04, 0x03, 0x13, 0x07, 0x73, 0x61, 0x6E, 0x67, 0x66, 0x6F, 0x72, 0x31, 0x22,
		0x30, 0x20, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x13, 0x73,
		0x61, 0x6E, 0x67, 0x66, 0x6F, 0x72, 0x40, 0x73, 0x61, 0x6E, 0x67, 0x66, 0x6F, 0x72, 0x2E, 0x63,
		0x6F, 0x6D, 0x30, 0x1E, 0x17, 0x0D, 0x31, 0x35, 0x31, 0x30, 0x31, 0x32, 0x30, 0x38, 0x35, 0x37,
		0x34, 0x38, 0x5A, 0x17, 0x0D, 0x32, 0x35, 0x31, 0x30, 0x31, 0x31, 0x30, 0x38, 0x35, 0x37, 0x34,
		0x38, 0x5A, 0x30, 0x7D, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06, 0x13, 0x02, 0x43,
		0x4E, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x08, 0x13, 0x02, 0x47, 0x44, 0x31, 0x0B,
		0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x07, 0x13, 0x02, 0x53, 0x5A, 0x31, 0x10, 0x30, 0x0E, 0x06,
		0x03, 0x55, 0x04, 0x0A, 0x13, 0x07, 0x53, 0x41, 0x4E, 0x47, 0x46, 0x4F, 0x52, 0x31, 0x0C, 0x30,
		0x0A, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x13, 0x03, 0x53, 0x53, 0x4C, 0x31, 0x10, 0x30, 0x0E, 0x06,
		0x03, 0x55, 0x04, 0x03, 0x13, 0x07, 0x73, 0x61, 0x6E, 0x67, 0x66, 0x6F, 0x72, 0x31, 0x22, 0x30,
		0x20, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16, 0x13, 0x73, 0x61,
		0x6E, 0x67, 0x66, 0x6F, 0x72, 0x40, 0x73, 0x61, 0x6E, 0x67, 0x66, 0x6F, 0x72, 0x2E, 0x63, 0x6F,
		0x6D, 0x30, 0x59, 0x30, 0x13, 0x06, 0x07, 0x2A, 0x86, 0x48, 0xCE, 0x3D, 0x02, 0x01, 0x06, 0x08,
		0x2A, 0x81, 0x1C, 0xCF, 0x55, 0x01, 0x82, 0x2D, 0x03, 0x42, 0x00, 0x04, 0x19, 0x79, 0x5D, 0xF7,
		0x01, 0xF3, 0x9D, 0x1F, 0xB2, 0x20, 0xC4, 0x5F, 0xA7, 0xFA, 0x4E, 0xBF, 0xAD, 0xD1, 0x70, 0x25,
		0x37, 0xB9, 0x46, 0xCD, 0x3D, 0x48, 0x04, 0xB3, 0x7F, 0xBC, 0x3E, 0xA5, 0x2B, 0x2C, 0xEE, 0xD6,
		0xCC, 0x04, 0x2B, 0x5B, 0xBB, 0x56, 0x8D, 0xED, 0x3B, 0x36, 0x73, 0xF2, 0x88, 0xE1, 0x9C, 0xC4,
		0x9A, 0xE3, 0xC3, 0x50, 0xD2, 0xB8, 0x09, 0x03, 0xD8, 0x6D, 0x91, 0x2C, 0xA3, 0x82, 0x01, 0x0F,
		0x30, 0x82, 0x01, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x1D, 0x13, 0x04, 0x02, 0x30, 0x00, 0x30,
		0x2C, 0x06, 0x09, 0x60, 0x86, 0x48, 0x01, 0x86, 0xF8, 0x42, 0x01, 0x0D, 0x04, 0x1F, 0x16, 0x1D,
		0x4F, 0x70, 0x65, 0x6E, 0x53, 0x53, 0x4C, 0x20, 0x47, 0x65, 0x6E, 0x65, 0x72, 0x61, 0x74, 0x65,
		0x64, 0x20, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x30, 0x1D, 0x06,
		0x03, 0x55, 0x1D, 0x0E, 0x04, 0x16, 0x04, 0x14, 0x50, 0x30, 0xB3, 0x89, 0x44, 0xF0, 0xB5, 0x9B,
		0x21, 0xA1, 0xCF, 0xC6, 0xE3, 0x80, 0xF0, 0xD5, 0x4D, 0xE5, 0x22, 0x25, 0x30, 0x81, 0xB0, 0x06,
		0x03, 0x55, 0x1D, 0x23, 0x04, 0x81, 0xA8, 0x30, 0x81, 0xA5, 0x80, 0x14, 0xEC, 0x25, 0x01, 0xB6,
		0x82, 0x08, 0xE1, 0xC3, 0xDF, 0x2D, 0x8D, 0x0B, 0xD6, 0xB5, 0x49, 0x4C, 0x05, 0x8F, 0x32, 0x14,
		0xA1, 0x81, 0x81, 0xA4, 0x7F, 0x30, 0x7D, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x06,
		0x13, 0x02, 0x43, 0x4E, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x08, 0x13, 0x02, 0x47,
		0x44, 0x31, 0x0B, 0x30, 0x09, 0x06, 0x03, 0x55, 0x04, 0x07, 0x13, 0x02, 0x53, 0x5A, 0x31, 0x10,
		0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x0A, 0x13, 0x07, 0x53, 0x41, 0x4E, 0x47, 0x46, 0x4F, 0x52,
		0x31, 0x0C, 0x30, 0x0A, 0x06, 0x03, 0x55, 0x04, 0x0B, 0x13, 0x03, 0x53, 0x53, 0x4C, 0x31, 0x10,
		0x30, 0x0E, 0x06, 0x03, 0x55, 0x04, 0x03, 0x13, 0x07, 0x73, 0x61, 0x6E, 0x67, 0x66, 0x6F, 0x72,
		0x31, 0x22, 0x30, 0x20, 0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x09, 0x01, 0x16,
		0x13, 0x73, 0x61, 0x6E, 0x67, 0x66, 0x6F, 0x72, 0x40, 0x73, 0x61, 0x6E, 0x67, 0x66, 0x6F, 0x72,
		0x2E, 0x63, 0x6F, 0x6D, 0x82, 0x09, 0x00, 0x9D, 0x1D, 0x78, 0xA7, 0x1F, 0xAC, 0xEE, 0xA3, 0x30,
		0x0A, 0x06, 0x08, 0x2A, 0x81, 0x1C, 0xCF, 0x55, 0x01, 0x83, 0x75, 0x03, 0x48, 0x00, 0x30, 0x45,
		0x02, 0x21, 0x00, 0xAF, 0x31, 0xCD, 0x66, 0xB9, 0x81, 0x85, 0x34, 0x17, 0xC3, 0x75, 0x6B, 0xE6,
		0xFA, 0x5F, 0xD6, 0x57, 0x94, 0xC2, 0x2A, 0x31, 0xB3, 0x06, 0xC1, 0x84, 0xB6, 0xDC, 0x64, 0xF1,
		0x47, 0x50, 0x42, 0x02, 0x20, 0x3F, 0xE1, 0x92, 0x66, 0x2A, 0xCB, 0xD2, 0x9D, 0xBE, 0x89, 0x99,
		0x98, 0x7E, 0xA2, 0x57, 0xB8, 0x65, 0xCB, 0x61, 0xE7, 0xC3, 0xD5, 0x8D, 0x08, 0xC5, 0xB3, 0x9D,
		0x3E, 0x59, 0x27, 0xB6, 0x5F
	};


	u32 ret, len = 128;
	ECCPUBLICKEYBLOB pub;
	HANDLE hKey;
	BLOCKCIPHERPARAM bp;
	u8 encryptkey[1024];
	u8 key[16] = {0x47, 0x50, 0x42, 0x02, 0x20, 0x3F, 0xE1, 0x92, 0x66, 0x2A, 0xCB, 0xD2, 0x9D, 0, 0, 0};
	PENVELOPEDKEYBLOB env = (PENVELOPEDKEYBLOB)encryptkey;

	bp.PaddingType = 0;
	bp.IVLen = 0;
	bp.FeedBitLen = 0;

	memset(encryptkey, 0 ,1024);

	env->Version = 1;
	env->ulBits = 256;
	env->PubKey.BitLen = 256;
	env->ulSymmAlgID = SGD_SM1_ECB;
	memcpy(env->PubKey.XCoordinate + 32, keypair, 32);
	memcpy(env->PubKey.YCoordinate + 32, keypair + 32, 32);

	len = sizeof(ECCPUBLICKEYBLOB);
	ret = SKF_ExportPublicKey(hCont, 1, (u8*)&pub, (u32*)&len);
	if(ret) 
	{
		if(ret == SAR_KEYNOTFOUNTERR)
		{
			ret = SKF_GenECCKeyPair(hCont, SGD_SM2_1, &pub);
			if(ret) 
			{
				LOG("SKF_GenECCKeyPair error : 0x%x----%d\n",(u32)ret,__LINE__);
				return ret;
			}
			LOG("SKF_GenECCKeyPair OK\n");
		}
		else
		{
			LOG("SKF_ExportPublicKey error : 0x%x----%d\n",(u32)ret,__LINE__);
			return ret;
		}
	}
	LOG("SKF_ExportPublicKey OK\n");

	ret = SKF_ExtECCEncrypt(hDev, &pub, key, 16, &env->ECCCipherBlob);
	if(ret)
	{
		LOG("SKF_ExtECCEncrypt error : 0x%x----%d\n",(u32)ret,__LINE__);
		return ret;
	}
	LOG("SKF_ExtECCEncrypt OK\n");
	ret = SKF_SetSymmKey(hDev, key, SGD_SM1_ECB, &hKey);
	if(ret)
	{
		LOG("SKF_SetSymmKey error : 0x%x----%d\n",(u32)ret,__LINE__);
		return ret;
	}
	LOG("SKF_ExtECCEncrypt OK\n");

	ret = SKF_EncryptInit(hKey, bp);
	if(ret)
	{
		LOG("SKF_EncryptInit error : 0x%x----%d\n",(u32)ret,__LINE__);
		return ret;
	}
	LOG("SKF_EncryptInit OK\n");
	len = 32;
	//ret = SKF_Encrypt(hKey, keypair + 64, 32, env->pbEncryptedPriKey + 32, &len);
	ret = SKF_Encrypt(hKey, keypair + 64, 32, env->cbEncryptedPriKey + 32, &len);
	if(ret)
	{
		LOG("SKF_Encrypt error : 0x%x----%d\n",(u32)ret,__LINE__);
		return ret;
	}
	LOG("SKF_Encrypt OK\n");	
	
	ret = SKF_ImportECCKeyPair(hCont, env);
	if(ret)
	{
		LOG("SKF_ImportECCKeyPair error : 0x%x----%d\n",(u32)ret,__LINE__);
		return ret;
	}
	LOG("SKF_ImportECCKeyPair OK\n");
	len = sizeof(ECCPUBLICKEYBLOB);
	ret = SKF_ExportPublicKey(hCont, 0, (u8*)&pub, &len);
	if(ret)
	{
		LOG("SKF_ExportPublicKey error : 0x%x----%d\n", (u32)ret,__LINE__);
		return ret;
	}
	LOG("SKF_ExportPublicKey OK\n");
	ret = SKF_ECCExportSessionKey(hCont, SGD_SM1_ECB, &pub, &env->ECCCipherBlob, &hKey);
	if(ret)
	{
		LOG("SKF_ECCExportSessionKey error : 0x%x----%d\n", (u32)ret,__LINE__);
		return ret;
	}
	LOG("SKF_ECCExportSessionKey OK\n");
	ret = SKF_ImportSessionKey(hCont, SGD_SM1_ECB, (u8*)&env->ECCCipherBlob, sizeof(ECCCIPHERBLOB) + env->ECCCipherBlob.CipherLen - 1, &hKey);
	if(ret)
	{
		LOG("SKF_ImportSessionKey error : 0x%x----%d\n",(u32)ret,__LINE__);
		return ret;
	}
	LOG("SKF_ImportSessionKey OK\n");
	
	ret = SKF_ImportCertificate(hCont, 0, cert, sizeof(cert));
	if(ret)
	{
		LOG("SKF_ImportCertificate error : 0x%x----%d\n",(u32)ret,__LINE__);
		return ret;
	}
	LOG("SKF_ImportCertificate OK\n");
	return 0;
}
u32 CTN_Test(DEVHANDLE hDev,HAPPLICATION hApp)
{
	HCONTAINER hCtn;
	char *p;
	u32 r, t;
	u8 data[1024];
	char szList[260] = {0};
	int len = 260;

	r = SKF_EnumContainer(hApp, szList, &len);
	if(r)
	{
		LOG("SKF_EnumContainer error 0x%x\n", r);
		return r;
	}
	LOG("SKF_EnumContainer OK\n");
	
	r = SKF_CreateContainer(hApp, "CTN_CCORE", &hCtn);
	if(r)
	{
		if(r != 0xa000030)
		{
			LOG("SKF_CreateContainer error 0x%x--%d\n", r,__LINE__);
			return r;
		}
	}
	LOG("SKF_CreateContainer OK\n");
	
	r = SKF_GetContainerType(hCtn, &t);
	if(r) 
	{
		LOG("SKF_GetContainerType error 0x%x\n", r);
		return r;
	}
	LOG("SKF_GetContainerType OK\n");
	
	SKF_CloseContainer(hCtn);
	LOG("SKF_CloseContainer OK\n");
	
	r = SKF_DeleteContainer(hApp, "CTN_CCORE");
	if(r)
	{
			LOG("SKF_DeleteContainer error 0x%x\n", r);
			return r;
	}
	LOG("SKF_DeleteContainer OK\n");

	r = SKF_CreateContainer(hApp, "TestSKF_RSA", &hCtn);
	if(r)
	{
		if(r != 0xa00002F)
		{
			LOG("SKF_CreateContainer error 0x%x\n", r);
			return r;
		}
		else
		{
			r = SKF_OpenContainer(hApp, "TestSKF_RSA", &hCtn);
			if(r)
			{
					LOG("SKF_OpenContainer error 0x%x\n", r);
					return r;	
			}
			LOG("SKF_OpenContainer OK\n");
		}
	}
	else
	{
		LOG("SKF_CreateContainer OK\n");
	}
	
	r = SKF_GenRSAKeyPair(hCtn, 1024, (PRSAPUBLICKEYBLOB)data);
	if(r) 
	{
		LOG("SKF_GenECCKeyPair error : 0x%x\n", r);
		return r;
	}
	LOG("SKF_GenRSAKeyPair OK\n");
	//2020.11.12
	//r = RSA_ImportKeyAndCert(hDev,hCtn);

	SKF_CloseContainer(hCtn);
	LOG("SKF_CloseContainer OK\n");

	r = SKF_CreateContainer(hApp, "TestSKF_ECC", &hCtn);
	if(r)
	{
		if(r != 0xa00002F)
		{
			LOG("SKF_CreateContainer error 0x%x\n", r);
			return r;
		}
		else
		{
				r = SKF_OpenContainer(hApp, "TestSKF_ECC", &hCtn);
				if(r)
				{
					LOG("SKF_OpenContainer error 0x%x\n", r);
					return r;	
				}
				else
				{
						LOG("SKF_OpenContainer OK\n");
				}
		}
	}
	else
	{
		LOG("SKF_CreateContainer OK\n");
	}

	r = SKF_GenECCKeyPair(hCtn, SGD_SM2_1, (PECCPUBLICKEYBLOB)data);
	if(r) 
	{
		LOG("SKF_GenECCKeyPair for SIGN KEY error : 0x%x\n", r);
		return r;
	}
	LOG("SKF_GenECCKeyPair OK\n");
	
	//mw2020.11.12
	r = SKF_VerifyPIN(hApp, USER_TYPE,"111111",&len);
	if(r)
	{
		LOG("SKF_VerifyPIN error : 0x%x----%d\n",r,__LINE__);
		return r;
	}

	ECC_ImportKeyAndCert(hDev,hCtn);
	
	SKF_CloseContainer(hCtn);
	LOG("SKF_CloseContainer OK\n");
	return 0;
}
int main11()
{
	int ret = 0;
	char szList[260] = {0};
	char *p = NULL;
	unsigned char sendData[512] = {0};
	unsigned char recvData[512] = {0};
	unsigned int sendLen = 512;
	unsigned int recvLen = 512;
	DEVHANDLE hDev;
	u32 len = 260;
	ret = SKF_EnumDev(1, szList, &len);
	if(ret)
	{
		printf("SKF_EnumDev fail err:0x%08x\n",ret);
		goto END;
	}
	
	printf("Enum OK ,device is :%s ,dev list len:%d\n",szList,len);
	if (len<=2)
	{
		printf("SKF_EnumDev error ,no device found \n");
		goto END;
	}
	p = szList;
	while(p&&p[0])
	{
			printf("%s\n",p);
			p += (strlen(p) + 1);	
	}
	printf("SKF_EnumDev OK \n");
	ret = SKF_ConnectDev(szList, &hDev);
	if(ret)
	{
		printf("SKF_ConnectDev error 0x%08x\n", ret);
		goto END;
	}
	printf("SKF_ConnectDev OK \n");
	
	sendData[0] = 0x80;
	sendData[1] = 0x0A;
	sendData[2] = 0x00;
	sendData[3] = 0x00;
	sendLen = 4;
	ret = SKF_Transmit(hDev,sendData,sendLen,recvData,&recvLen);
	if(ret)
	{
			printf("SKF_Transmit error 0x%08x\n", ret);
			goto END;
	}
	printf("SKF_Transmit OK \n");
	
	ret = Sym_Test(hDev,SGD_SMS4_ECB);
	if(ret)
	{
		goto END;
	}
	
	END:
	SKF_DisConnectDev(hDev);
	printf("SKF_DisConnectDev OK \n");
	return 0;
}
int main()
{	
	int nRetCode = 0;
	BOOL blLoad = FALSE;
	int ret = 0;
	int i = 0;
	unsigned char sendData[512] = {0};
	unsigned char recvData[512] = {0};
	unsigned int sendLen = 512;
	unsigned int recvLen = 512;
	
	DEVHANDLE hDev;
	HAPPLICATION hApp;
	HCONTAINER hCtn;
	unsigned char randData[1024] = {0};
	BLOCKCIPHERPARAM bp;
	HANDLE hKey = NULL;
	u32 status = 0;
	u8 key[16] = {0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15};
	u8 authKey[16] = {0x43,0x2A,0x43,0x4F,0x52,0x45,0x20,0x53,0x59,0x53,0x20,0x40,0x20,0x53,0x5A,0x20};//{0xEE,0xED,0xF4,0xA7,0x48,0x50,0x4D,0x6F,0x88,0x59,0xB4,0x40,0x18,0x4D,0x85,0x1E};
	u8 data[256] = {0}, endata[256], decdata[256];
	char szList[260] = {0};
	u32 len = 260;
	DEVINFO info;
	char *p = NULL;

	memset(szList,0,260);
	
	ret = SKF_EnumDev(1, szList, &len);

	if(ret)
	{
		printf("SKF_EnumDev fail err:0x%08x\n",ret);
		goto END;
	}
	
	printf("Enum OK ,device is :%s ,dev list len:%d\n",szList,len);
	if (len<=2)
	{
		printf("SKF_EnumDev error ,no device found \n");
		goto END;
	}
	p = szList;
	while(p&&p[0])
	{
			printf("%s\n",p);
			p += (strlen(p) + 1);	
	}
	printf("SKF_EnumDev OK \n");
	ret = SKF_ConnectDev(szList, &hDev);
	if(ret)
	{
		printf("SKF_ConnectDev error 0x%08x\n", ret);
		goto END;
	}
	printf("SKF_ConnectDev OK \n");
	
/*	ret = SKF_GetDevState(szList,&status);
	if(ret)
	{

			printf("SKF_GetDevState error 0x%08x\n", ret);
			goto CLOSE_DEV;
	}
	printf("SKF_GetDevState OK \n");
	ret = SKF_SetLabel(hDev,"cfli");
	if(ret)
	{

			printf("SKF_SetLabel error 0x%08x\n", ret);
			goto CLOSE_DEV;
	}
	printf("SKF_SetLabel OK \n");
	ret = SKF_GetDevInfo(hDev, &info);
	if(ret)
	{

			printf("SKF_GetDevInfo error 0x%08x\n", ret);
			goto CLOSE_DEV;
	}
	printf("SKF_GetDevInfo OK \n");

	sendData[0] = 0x80;
	sendData[1] = 0x0A;
	sendData[2] = 0x00;
	sendData[3] = 0x00;
	sendLen = 4;
	ret = SKF_Transmit(hDev,sendData,sendLen,recvData,&recvLen);
	if(ret)
	{
			printf("SKF_Transmit error 0x%08x\n", ret);
			goto CLOSE_DEV;
	}
	printf("SKF_Transmit OK \n");
	
	ret = ChangeDevAuth(hDev,"0123456789abcdef",authKey);
	if(ret)
	{
			printf("ChangeDevAuth error 0x%08x \n", ret);
			goto CLOSE_DEV;
	}
	printf("ChangeDevAuth OK \n");
*/	
	ret = SKF_GenRandom(hDev,randData,512);
	if(ret)
	{
			printf("SKF_GenRandom error 0x%08x \n", ret);
			goto CLOSE_APP;
	}
	printf("SKF_GenRandom OK \n");
	
	ret = SKF_LockDev(hDev,-1);
	if(ret)
	{
			printf("SKF_LockDev error 0x%08x\n", ret);
			goto CLOSE_DEV;	
	}
	printf("SKF_LockDev OK \n");
	
	ret = SKF_UnlockDev(hDev);
	if(ret)
	{
			printf("SKF_UnlockDev error 0x%08x\n", ret);
			goto CLOSE_DEV;	
	}
	printf("SKF_UnlockDev OK \n");
	
	ret = Sym_Test(hDev,SGD_SMS4_ECB);
	if(ret)
	{
		goto CLOSE_APP;
	}
	
	ret = CreApp_Test(hDev,&hApp);
	if(ret)
	{
		goto CLOSE_DEV;
	}
	ret = SKF_VerifyPIN(hApp, USER_TYPE, "111111", &len);
	if(ret)
	{
		SKF_CloseApplication(hApp);
		LOG("SKF_VerifyPIN error 0x%x, retry = %d \n", ret, len);
		return 1;
	}
	ret = File_Test(hApp, "111111");
	if(ret)
	{
		goto CLOSE_APP;
	}
	////////////////////////////////////////////////////////////
	printf("SKF_VerifyPIN OK \n");
	ret = CTN_Test(hDev,hApp);
	if(ret)
	{
		goto CLOSE_APP;
	}
	printf("-------------------------------------OK-----------------------------------\n");
CLOSE_ALL:
		//SKF_CloseContainer(hCtn);
CLOSE_APP:
		SKF_CloseApplication(hApp);
CLOSE_DEV:
		SKF_DisConnectDev(hDev);
END:
	printf("end\n");
	return 0;
}
