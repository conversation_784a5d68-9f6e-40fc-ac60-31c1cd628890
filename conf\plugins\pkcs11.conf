pkcs11 {

    # Whether to load the plugin. Can also be an integer to increase the
    # priority of this plugin.
    load = yes

    # Reload certificates from all tokens if charo<PERSON> receives a SIGHUP.
    # reload_certs = no

    # Whether the PKCS#11 modules should be used for DH and ECDH (see use_ecc
    # option).
    # use_dh = no

    # Whether the PKCS#11 modules should be used for ECDH and ECDSA public key
    # operations. ECDSA private keys can be used regardless of this option.
    # use_ecc = no

    # Whether the PKCS#11 modules should be used to hash data.
    # use_hasher = no

    # Whether the PKCS#11 modules should be used for public key operations, even
    # for keys not stored on tokens.
    # use_pubkey = no

    # Whether the PKCS#11 modules should be used as RNG.
    # use_rng = no

    # List of available PKCS#11 modules.
    modules {

        <name> {

            # Whether to automatically load certificates from tokens.
            # load_certs = yes

            # Whether OS locking should be enabled for this module.
            # os_locking = no

            # Full path to the shared object file of this PKCS#11 module.
            # path =

        }

    }

}

