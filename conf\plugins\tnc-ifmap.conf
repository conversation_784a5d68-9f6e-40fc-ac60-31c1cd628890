tnc-ifmap {

    # Path to X.509 certificate file of IF-MAP client.
    # client_cert =

    # Path to private key file of IF-MAP client.
    # client_key =

    # Unique name of strongSwan server as a PEP and/or PDP device.
    # device_name =

    # Whether to load the plugin. Can also be an integer to increase the
    # priority of this plugin.
    load = yes

    # Interval in seconds between periodic IF-MAP RenewSession requests.
    # renew_session_interval = 150

    # Path to X.509 certificate file of IF-MAP server.
    # server_cert =

    # URI of the form [https://]servername[:port][/path].
    # server_uri = https://localhost:8444/imap

    # Credentials of IF-MAP client of the form username:password. If set, make
    # sure to adjust the permissions of the config file accordingly.
    # username_password =

}

