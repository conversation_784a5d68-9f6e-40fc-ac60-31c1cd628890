#!/bin/bash

# 重新构建strongSwan并启用gmalg插件的脚本

echo "开始重新构建strongSwan并启用gmalg插件..."

# 进入strongswan目录
cd /home/<USER>/strongswan-5.9.30

echo "1. 检查内核国密算法支持..."
# 运行内核算法检测脚本
if [ -f "../test_kernel_gmalg.sh" ]; then
    chmod +x ../test_kernel_gmalg.sh
    ../test_kernel_gmalg.sh
    echo ""
fi

echo "2. 清理之前的配置和编译结果..."
make distclean > /dev/null 2>&1

echo "3. 重新生成配置脚本..."
autoreconf -fiv

echo "4. 重新配置strongSwan..."
# 注意：使用与您之前相同的配置参数
./configure \
  --prefix=/usr \
  --sysconfdir=/etc \
  --enable-stroke \
  --enable-kernel-libipsec \
  --enable-gmalg --with-gmalg_interior=yes \
  --enable-openssl \
  --enable-gmp

if [ $? -ne 0 ]; then
    echo "配置失败！"
    echo "请检查："
    echo "- 是否安装了必要的开发工具和库"
    echo "- 内核是否支持AF_ALG接口"
    echo "- 国密算法是否已编译到内核中"
    exit 1
fi

echo "配置成功！"

echo "5. 开始编译..."
make -j4

if [ $? -eq 0 ]; then
    echo "编译成功！"
    echo ""
    echo "6. 安装strongSwan..."
    echo "运行安装命令..."
    sudo make install

    if [ $? -eq 0 ]; then
        echo "安装成功！"
        echo ""
        echo "7. 安装gmalg插件配置文件..."
        if [ -f "conf/plugins/gmalg.conf" ]; then
            sudo mkdir -p /etc/strongswan.d/charon
            sudo cp conf/plugins/gmalg.conf /etc/strongswan.d/charon/
            echo "   gmalg配置文件已安装到 /etc/strongswan.d/charon/gmalg.conf"
        fi

        echo ""
        echo "8. 运行诊断脚本..."
        if [ -f "../diagnose_gmalg.sh" ]; then
            chmod +x ../diagnose_gmalg.sh
            ../diagnose_gmalg.sh
        fi

        echo ""
        echo "9. 验证gmalg插件..."
        echo "重启strongSwan服务..."
        sudo systemctl restart strongswan
        sleep 3

        echo "检查插件加载状态..."
        ipsec statusall | grep -i gmalg

        if [ $? -eq 0 ]; then
            echo "✓ gmalg插件已成功加载！"
        else
            echo "⚠ gmalg插件可能未加载，运行详细诊断："
            echo ""
            echo "完整的插件列表："
            ipsec statusall | grep "loaded plugins"
            echo ""
            echo "检查strongSwan日志："
            sudo journalctl -u strongswan -n 20 --no-pager
        fi
    else
        echo "安装失败！"
        exit 1
    fi
else
    echo "编译失败！"
    echo "请检查编译错误信息"
    exit 1
fi

echo ""
echo "构建和安装完成！"
echo ""
echo "如果gmalg插件未正确加载，请："
echo "1. 检查内核是否支持国密算法：cat /proc/crypto | grep -i sm"
echo "2. 检查strongSwan日志：sudo journalctl -u strongswan -f"
echo "3. 手动测试AF_ALG接口：运行 test_kernel_gmalg.sh 脚本"
