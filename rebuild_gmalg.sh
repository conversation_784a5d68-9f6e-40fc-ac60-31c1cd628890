#!/bin/bash

# 重新构建strongSwan并启用gmalg插件的脚本

echo "开始重新构建strongSwan并启用gmalg插件..."

# 进入strongswan目录
cd /home/<USER>/strongswan-5.9.30

# 清理之前的配置和编译结果
echo "清理之前的配置和编译结果..."
make distclean > /dev/null 2>&1

# 重新生成配置脚本
echo "重新生成配置脚本..."
autoreconf -fiv

# 重新配置（注意：gmalg_interior设置为no，因为我们使用内置实现）
echo "重新配置strongSwan..."
./configure \
  --prefix=/usr \
  --sysconfdir=/etc \
  --enable-stroke \
  --enable-gmalg \
  --enable-openssl \
  --enable-gmp \
  --with-gmalg_interior=no

if [ $? -ne 0 ]; then
    echo "配置失败！"
    exit 1
fi

echo "配置成功！"

# 编译
echo "开始编译..."
make -j4

if [ $? -eq 0 ]; then
    echo "编译成功！"
    echo "现在可以运行 'sudo make install' 来安装"
    echo ""
    echo "安装后，可以通过以下命令验证gmalg插件是否加载："
    echo "sudo systemctl restart strongswan"
    echo "ipsec statusall"
else
    echo "编译失败！"
    exit 1
fi

echo "构建完成！"
