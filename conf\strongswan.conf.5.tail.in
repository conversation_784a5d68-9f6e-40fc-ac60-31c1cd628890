.SH LOGGER CONFIGURATION
Options in
.BR strongswan.conf (5)
provide a much more flexible way to configure loggers for the IKE daemon charon
than using the
.B charondebug
option in
.BR ipsec.conf (5).
.PP
.BR Note :
If any loggers are specified in strongswan.conf,
.B charondebug
does not have any effect.
.PP
There are currently two types of loggers:
.TP
.B File loggers
Log directly to a file and are defined by specifying an arbitrarily named
subsection in the
.B charon.filelog
section. The full path to the file is configured in the \fIpath\fR setting of
that subsection, however, if it only contains characters permitted in section
names, the setting may also be omitted and the path specified as name of the
subsection. To log to the console the two special filenames
.BR stdout " and " stderr
may be used.
.TP
.B Syslog loggers
Log into a syslog facility and are defined by specifying the facility to log to
as the name of a subsection in the
.B charon.syslog
section. The following facilities are currently supported:
.BR daemon " and " auth .
.PP
Multiple loggers can be defined for each type with different log verbosity for
the different subsystems of the daemon.

.SS Subsystems
.TP
.B dmn
Main daemon setup/cleanup/signal handling
.TP
.B mgr
IKE_SA manager, handling synchronization for IKE_SA access
.TP
.B ike
IKE_SA
.TP
.B chd
CHILD_SA
.TP
.B job
Jobs queueing/processing and thread pool management
.TP
.B cfg
Configuration management and plugins
.TP
.B knl
IPsec/Networking kernel interface
.TP
.B net
IKE network communication
.TP
.B asn
Low-level encoding/decoding (ASN.1, X.509 etc.)
.TP
.B enc
Packet encoding/decoding encryption/decryption operations
.TP
.B tls
libtls library messages
.TP
.B esp
libipsec library messages
.TP
.B lib
libstrongswan library messages
.TP
.B tnc
Trusted Network Connect
.TP
.B imc
Integrity Measurement Collector
.TP
.B imv
Integrity Measurement Verifier
.TP
.B pts
Platform Trust Service
.SS Loglevels
.TP
.B -1
Absolutely silent
.TP
.B 0
Very basic auditing logs, (e.g. SA up/SA down)
.TP
.B 1
Generic control flow with errors, a good default to see what's going on
.TP
.B 2
More detailed debugging control flow
.TP
.B 3
Including RAW data dumps in Hex
.TP
.B 4
Also include sensitive material in dumps, e.g. keys
.SS Example
.PP
.EX
	charon {
		filelog {
			charon {
				path = /var/log/charon.log
				time_format = %b %e %T
				append = no
				default = 1
			}
			stderr {
				ike = 2
				knl = 3
				ike_name = yes
			}
		}
		syslog {
			# enable logging to LOG_DAEMON, use defaults
			daemon {
			}
			# minimalistic IKE auditing logging to LOG_AUTHPRIV
			auth {
				default = -1
				ike = 0
			}
		}
	}
.EE

.SH JOB PRIORITY MANAGEMENT
Some operations in the IKEv2 daemon charon are currently implemented
synchronously and blocking. Two examples for such operations are communication
with a RADIUS server via EAP-RADIUS, or fetching CRL/OCSP information during
certificate chain verification. Under high load conditions, the thread pool may
run out of available threads, and some more important jobs, such as liveness
checking, may not get executed in time.
.PP
To prevent thread starvation in such situations job priorities were introduced.
The job processor will reserve some threads for higher priority jobs, these
threads are not available for lower priority, locking jobs.
.SS Implementation
Currently 4 priorities have been defined, and they are used in charon as
follows:
.TP
.B CRITICAL
Priority for long-running dispatcher jobs.
.TP
.B HIGH
INFORMATIONAL exchanges, as used by liveness checking (DPD).
.TP
.B MEDIUM
Everything not HIGH/LOW, including IKE_SA_INIT processing.
.TP
.B LOW
IKE_AUTH message processing. RADIUS and CRL fetching block here
.PP
Although IKE_SA_INIT processing is computationally expensive, it is explicitly
assigned to the MEDIUM class. This allows charon to do the DH exchange while
other threads are blocked in IKE_AUTH. To prevent the daemon from accepting more
IKE_SA_INIT requests than it can handle, use IKE_SA_INIT DROPPING.
.PP
The thread pool processes jobs strictly by priority, meaning it will consume all
higher priority jobs before looking for ones with lower priority. Further, it
reserves threads for certain priorities. A priority class having reserved
.I n
threads will always have
.I n
threads available for this class (either currently processing a job, or waiting
for one).
.SS Configuration
To ensure that there are always enough threads available for higher priority
tasks, threads must be reserved for each priority class.
.TP
.BR charon.processor.priority_threads.critical " [0]"
Threads reserved for CRITICAL priority class jobs
.TP
.BR charon.processor.priority_threads.high " [0]"
Threads reserved for HIGH priority class jobs
.TP
.BR charon.processor.priority_threads.medium " [0]"
Threads reserved for MEDIUM priority class jobs
.TP
.BR charon.processor.priority_threads.low " [0]"
Threads reserved for LOW priority class jobs
.PP
Let's consider the following configuration:
.PP
.EX
	charon {
		processor {
			priority_threads {
				high = 1
				medium = 4
			}
		}
	}
.EE
.PP
With this configuration, one thread is reserved for HIGH priority tasks. As
currently only liveness checking and stroke message processing is done with
high priority, one or two threads should be sufficient.
.PP
The MEDIUM class mostly processes non-blocking jobs. Unless your setup is
experiencing many blocks in locks while accessing shared resources, threads for
one or two times the number of CPU cores is fine.
.PP
It is usually not required to reserve threads for CRITICAL jobs. Jobs in this
class rarely return and do not release their thread to the pool.
.PP
The remaining threads are available for LOW priority jobs. Reserving threads
does not make sense (until we have an even lower priority).
.SS Monitoring
To see what the threads are actually doing, invoke
.IR "ipsec statusall" .
Under high load, something like this will show up:
.PP
.EX
	worker threads: 2 or 32 idle, 5/1/2/22 working,
		job queue: 0/0/1/149, scheduled: 198
.EE
.PP
From 32 worker threads,
.IP 2
are currently idle.
.IP 5
are running CRITICAL priority jobs (dispatching from sockets, etc.).
.IP 1
is currently handling a HIGH priority job. This is actually the thread currently
providing this information via stroke.
.IP 2
are handling MEDIUM priority jobs, likely IKE_SA_INIT or CREATE_CHILD_SA
messages.
.IP 22
are handling LOW priority jobs, probably waiting for an EAP-RADIUS response
while processing IKE_AUTH messages.
.PP
The job queue load shows how many jobs are queued for each priority, ready for
execution. The single MEDIUM priority job will get executed immediately, as
we have two spare threads reserved for MEDIUM class jobs.

.SH IKE_SA_INIT DROPPING
If a responder receives more connection requests per seconds than it can handle,
it does not make sense to accept more IKE_SA_INIT messages. And if they are
queued but can't get processed in time, an answer might be sent after the
client has already given up and restarted its connection setup. This
additionally increases the load on the responder.
.PP
To limit the responder load resulting from new connection attempts, the daemon
can drop IKE_SA_INIT messages just after reception. There are two mechanisms to
decide if this should happen, configured with the following options:
.TP
.BR charon.init_limit_half_open " [0]"
Limit based on the number of half open IKE_SAs. Half open IKE_SAs are SAs in
connecting state, but not yet established.
.TP
.BR charon.init_limit_job_load " [0]"
Limit based on the number of jobs currently queued for processing (sum over all
job priorities).
.PP
The second limit includes load from other jobs, such as rekeying. Choosing a
good value is difficult and depends on the hardware and expected load.
.PP
The first limit is simpler to calculate, but includes the load from new
connections only. If your responder is capable of negotiating 100 tunnels/s, you
might set this limit to 1000. The daemon will then drop new connection attempts
if generating a response would require more than 10 seconds. If you are
allowing for a maximum response time of more than 30 seconds, consider adjusting
the timeout for connecting IKE_SAs
.RB ( charon.half_open_timeout ).
A responder, by default, deletes an IKE_SA if the initiator does not establish
it within 30 seconds. Under high load, a higher value might be required.

.SH LOAD TESTS
To do stability testing and performance optimizations, the IKE daemon charon
provides the \fIload-tester\fR plugin. This plugin allows one to setup thousands
of tunnels concurrently against the daemon itself or a remote host.
.PP
.B WARNING:
Never enable the load-testing plugin on productive systems. It provides
preconfigured credentials and allows an attacker to authenticate as any user.
.PP
.SS Configuration details
For public key authentication, the responder uses the
.B \(dqCN=srv, OU=load-test, O=strongSwan\(dq
identity. For the initiator, each connection attempt uses a different identity
in the form
.BR "\(dqCN=c1-r1, OU=load-test, O=strongSwan\(dq" ,
where the first number indicates the client number, the second the
authentication round (if multiple authentication rounds are used).
.PP
For PSK authentication, FQDN identities are used. The server uses
.BR srv.strongswan.org ,
the client uses an identity in the form
.BR c1-r1.strongswan.org .
.PP
For EAP authentication, the client uses a NAI in the form
.BR <EMAIL> .
.PP
To configure multiple authentication rounds, concatenate multiple methods using,
e.g.
.EX
	initiator_auth = pubkey|psk|eap-md5|eap-aka
.EE
.PP
The responder uses a hardcoded certificate based on a 1024-bit RSA key.
This certificate additionally serves as CA certificate. A peer uses the same
private key, but generates client certificates on demand signed by the CA
certificate. Install the Responder/CA certificate on the remote host to
authenticate all clients.
.PP
To speed up testing, the load tester plugin implements a special Diffie-Hellman
implementation called \fImodpnull\fR. By setting
.EX
	proposal = aes128-sha1-modpnull
.EE
this wicked fast DH implementation is used. It does not provide any security
at all, but allows one to run tests without DH calculation overhead.
.SS Examples
.PP
In the simplest case, the daemon initiates IKE_SAs against itself using the
loopback interface. This will actually establish double the number of IKE_SAs,
as the daemon is initiator and responder for each IKE_SA at the same time.
Installation of IPsec SAs would fail, as each SA gets installed twice. To
simulate the correct behavior, a fake kernel interface can be enabled which does
not install the IPsec SAs at the kernel level.
.PP
A simple loopback configuration might look like this:
.PP
.EX
	charon {
		# create new IKE_SAs for each CHILD_SA to simulate
		# different clients
		reuse_ikesa = no
		# turn off denial of service protection
		dos_protection = no

		plugins {
			load-tester {
				# enable the plugin
				enable = yes
				# use 4 threads to initiate connections
				# simultaneously
				initiators = 4
				# each thread initiates 1000 connections
				iterations = 1000
				# delay each initiation in each thread by 20ms
				delay = 20
				# enable the fake kernel interface to
				# avoid SA conflicts
				fake_kernel = yes
			}
		}
	}
.EE
.PP
This will initiate 4000 IKE_SAs within 20 seconds. You may increase the delay
value if your box can not handle that much load, or decrease it to put more
load on it. If the daemon starts retransmitting messages your box probably can
not handle all connection attempts.
.PP
The plugin also allows one to test against a remote host. This might help to
test against a real world configuration. A connection setup to do stress
testing of a gateway might look like this:
.PP
.EX
	charon {
		reuse_ikesa = no
		threads = 32

		plugins {
			load-tester {
				enable = yes
				# 10000 connections, ten in parallel
				initiators = 10
				iterations = 1000
				# use a delay of 100ms, overall time is:
				# iterations * delay = 100s
				delay = 100
				# address of the gateway
				remote = *******
				# IKE-proposal to use
				proposal = aes128-sha1-modp1024
				# use faster PSK authentication instead
				# of 1024bit RSA
				initiator_auth = psk
				responder_auth = psk
				# request a virtual IP using configuration
				# payloads
				request_virtual_ip = yes
				# enable CHILD_SA every 60s
				child_rekey = 60
			}
		}
	}
.EE

.SH IKEv2 RETRANSMISSION
Retransmission timeouts in the IKEv2 daemon charon can be configured globally
using the three keys listed below:
.PP
.RS
.nf
.BR charon.retransmit_base " [1.8]"
.BR charon.retransmit_timeout " [4.0]"
.BR charon.retransmit_tries " [5]"
.BR charon.retransmit_jitter " [0]"
.BR charon.retransmit_limit " [0]"
.fi
.RE
.PP
The following algorithm is used to calculate the timeout:
.PP
.EX
	relative timeout = retransmit_timeout * retransmit_base ^ (n-1)
.EE
.PP
Where
.I n
is the current retransmission count. The calculated timeout can't exceed the
configured retransmit_limit (if any), which is useful if the number of retries
is high.
.PP
If a jitter in percent is configured, the timeout is modified as follows:
.PP
.EX
	relative timeout -= random(0, retransmit_jitter * relative timeout)
.EE
.PP
Using the default values, packets are retransmitted in:

.TS
l r r
---
lB r r.
Retransmission	Relative Timeout	Absolute Timeout
1	4s	4s
2	7s	11s
3	13s	24s
4	23s	47s
5	42s	89s
giving up	76s	165s
.TE
.
.SH VARIABLES
.
The variables used above are configured as follows:

.nf
.na
${piddir}               @piddir@
${prefix}               @prefix@
${random_device}        @random_device@
${urandom_device}       @urandom_device@
.ad
.fi
.
.SH FILES
.
.nf
.na
/etc/strongswan.conf       configuration file
/etc/strongswan.d/         directory containing included config snippets
/etc/strongswan.d/charon/  plugin specific config snippets
.ad
.fi
.
.SH SEE ALSO
\fBipsec.conf\fR(5), \fBipsec.secrets\fR(5), \fBipsec\fR(8), \fBcharon-cmd\fR(8)

.SH HISTORY
Written for the
.UR http://www.strongswan.org
strongSwan project
.UE
by Tobias Brunner, Andreas Steffen and Martin Willi.
