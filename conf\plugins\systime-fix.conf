systime-fix {

    # Interval in seconds to check system time for validity. 0 disables the
    # check.
    # interval = 0

    # Whether to load the plugin. Can also be an integer to increase the
    # priority of this plugin.
    load = yes

    # Whether to use reauth or delete if an invalid cert lifetime is detected.
    # reauth = no

    # Threshold date where system time is considered valid. Disabled if not
    # specified.
    # threshold =

    # strptime(3) format used to parse threshold option.
    # threshold_format = %Y

    # How long to wait for a valid system time if an interval is configured. 0
    # to recheck indefinitely.
    # timeout = 0s

}

