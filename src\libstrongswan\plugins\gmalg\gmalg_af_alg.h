/*
 * gmalg_af_alg.h - strongSwan GMALG AF_ALG接口头文件
 * 定义与内核AF_ALG接口交互所需的数据结构和常量
 * 为strongSwan插件提供国密算法的完整接口定义
 */

#ifndef GMALG_AF_ALG_H_
#define GMALG_AF_ALG_H_

#include <stdint.h>
#include <stdbool.h>
#include <pthread.h>
#include "gmalg.h"  // 包含国密类型和函数声明


/* Linux AF_ALG相关头文件 */
#ifndef AF_ALG
#define AF_ALG 38
#endif

#ifndef SOL_ALG
#define SOL_ALG 279
#endif

#ifndef ALG_SET_KEY
#define ALG_SET_KEY 1
#endif


/* AF_ALG操作类型定义（与内核保持一致） */
#define ALG_OP_SM2_KEYGEN     1   /* SM2密钥生成 */
#define ALG_OP_SM2_SIGN       2   /* SM2签名 */
#define ALG_OP_SM2_VERIFY     3   /* SM2验证 */
#define ALG_OP_SM2_ENCRYPT    4   /* SM2加密 */
#define ALG_OP_SM2_DECRYPT    5   /* SM2解密 */
#define ALG_OP_SM2_PUBKEY_GEN 6   /* SM2公钥生成 */
#define ALG_OP_SM2_POINT_MUL  7   /* SM2点乘运算 */
#define ALG_OP_SM3_HASH       8   /* SM3哈希 */
#define ALG_OP_SM3_HMAC       9   /* SM3 HMAC */
#define ALG_OP_SM3_KDF        10  /* SM3密钥派生 */
#define ALG_OP_SM4_ENCRYPT    11  /* SM4加密 */
#define ALG_OP_SM4_DECRYPT    12  /* SM4解密 */

/* SM4工作模式定义 */
#define SM4_MODE_ECB          1   /* ECB模式 */
#define SM4_MODE_CBC          2   /* CBC模式 */
#define SM4_MODE_CFB          3   /* CFB模式 */
#define SM4_MODE_OFB          4   /* OFB模式 */
#define SM4_MODE_CTR          5   /* CTR模式 */

/* strongSwan兼容的请求响应数据结构 */
struct sm_alg_req {
    uint32_t operation;        /* 操作类型 */
    uint32_t key_len;         /* 密钥长度 */
    uint32_t data_len;        /* 数据长度 */
    uint32_t output_len;      /* 预期输出长度 */
    uint32_t user_id_len;     /* 用户ID长度（SM2专用） */
    uint32_t mode;            /* 工作模式（SM4专用） */
    uint32_t reserved[2];     /* 保留字段 */
    uint8_t data[];           /* 变长数据：key + data + user_id + iv */
} __attribute__((packed));

struct sm_alg_resp {
    uint32_t result;          /* 操作结果码 */
    uint32_t output_len;      /* 实际输出长度 */
    uint32_t reserved[2];     /* 保留字段 */
    uint8_t data[];           /* 输出数据 */
} __attribute__((packed));

/* GMALG设备句柄结构体声明 */
typedef struct gmalg_device_handle_st gmalg_device_handle;

/* 哈希上下文结构 */
struct gmalg_hash_context {
    uint8_t *buffer;          /* 数据缓冲区 */
    size_t buffer_len;        /* 当前数据长度 */
    size_t buffer_capacity;   /* 缓冲区容量 */
    bool finalized;           /* 是否已完成 */
};

/* 错误码定义 */
#define GMALG_SUCCESS         0    /* 操作成功 */
#define GMALG_ERROR_PARAM     -1   /* 参数错误 */
#define GMALG_ERROR_NOMEM     -2   /* 内存不足 */
#define GMALG_ERROR_NOKEY     -3   /* 密钥未设置 */
#define GMALG_ERROR_VERIFY    -4   /* 验证失败 */
#define GMALG_ERROR_CRYPTO    -5   /* 密码运算错误 */
#define GMALG_ERROR_NOTSUP    -6   /* 操作不支持 */

/* 调试宏定义 */
#ifdef GMALG_DEBUG_ENABLED
#define gmalg_debug(fmt, ...) printf("[GMALG] " fmt "\n", ##__VA_ARGS__)
#else
#define gmalg_debug(fmt, ...)
#endif

/* 内部函数声明 */

/**
 * 创建AF_ALG socket连接
 * @param type 算法类型（如"akcipher", "hash", "skcipher"）
 * @param name 算法名称（如"sm2", "sm3", "sm4"）
 * @return 成功返回socket描述符，失败返回-1
 */
int create_alg_socket(const char *type, const char *name);

/**
 * 发送AF_ALG请求并接收响应
 * @param fd socket描述符
 * @param req_data 请求数据指针
 * @param req_len 请求数据长度
 * @param resp_data 响应数据指针（输出参数，需要调用者释放）
 * @param resp_len 响应数据长度（输出参数）
 * @return 成功返回0，失败返回负错误码
 */
int send_alg_request(int fd, const void *req_data, size_t req_len, 
                     void **resp_data, size_t *resp_len);

/**
 * 设置AF_ALG socket密钥
 * @param fd socket描述符
 * @param key 密钥数据
 * @param key_len 密钥长度
 * @return 成功返回0，失败返回-1
 */
int set_alg_key(int fd, const void *key, size_t key_len);

/**
 * 内存安全清除函数
 * @param ptr 内存指针
 * @param size 清除大小
 */
void secure_memzero(void *ptr, size_t size);

/**
 * 便捷的SM3哈希函数
 * @param hDeviceHandle 设备句柄
 * @param pucData 待哈希的数据
 * @param uiDataLength 数据长度
 * @param pucHash 输出的哈希值（32字节）
 * @return 成功返回0，失败返回负错误码
 */
int GMALG_SM3Hash(void *hDeviceHandle, unsigned char *pucData, 
                  unsigned int uiDataLength, unsigned char *pucHash);

/* 密钥协商相关结构和函数 */

/**
 * 密钥协商句柄结构
 */
struct agreement_handle;

/* 全局变量声明 */
extern bool g_gmalg_initialized;
extern mutex_t *g_gmalg_mutex;
extern struct gmalg_hash_context g_hash_ctx;

/* 全局初始化和清理函数 */
int gmalg_global_init(void);
void gmalg_global_cleanup(void);

/* 兼容性宏定义 */
#define min(a, b) ((a) < (b) ? (a) : (b))
#define max(a, b) ((a) > (b) ? (a) : (b))

/* 网络字节序转换宏 */
#ifndef htonl
#include <arpa/inet.h>
#endif

/* 线程安全相关 - 使用strongSwan的mutex */
#include <threading/mutex.h>

/* 内存对齐宏 */
#ifndef __attribute__
#define __attribute__(x)
#endif

/* 编译时断言宏 */
#define STATIC_ASSERT(condition, message) \
    typedef char static_assertion_##message[(condition) ? 1 : -1]

/* 结构体大小检查 */
STATIC_ASSERT(sizeof(struct sm_alg_req) == 32, sm_alg_req_size_check);
STATIC_ASSERT(sizeof(struct sm_alg_resp) == 16, sm_alg_resp_size_check);

/* 函数属性定义 */
#ifdef __GNUC__
#define GMALG_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
#define GMALG_NONNULL(...) __attribute__((nonnull(__VA_ARGS__)))
#else
#define GMALG_WARN_UNUSED_RESULT
#define GMALG_NONNULL(...)
#endif

/* 注意：函数声明已在上面定义，这里不再重复声明以避免编译冲突 */

/* 调试和日志相关 */

/**
 * 设置调试级别
 * @param level 调试级别（0=关闭，1=错误，2=警告，3=信息，4=调试）
 */
void gmalg_set_debug_level(int level);

/**
 * 获取当前调试级别
 * @return 当前调试级别
 */
int gmalg_get_debug_level(void);

/**
 * 打印十六进制数据（用于调试）
 * @param name 数据名称
 * @param data 数据指针
 * @param len 数据长度
 */
void gmalg_print_hex(const char *name, const unsigned char *data, size_t len);

/* 性能统计相关 */

/**
 * 性能统计结构
 */
struct gmalg_perf_stats {
    uint64_t sm2_keygen_count;      /* SM2密钥生成次数 */
    uint64_t sm2_sign_count;        /* SM2签名次数 */
    uint64_t sm2_verify_count;      /* SM2验证次数 */
    uint64_t sm2_encrypt_count;     /* SM2加密次数 */
    uint64_t sm2_decrypt_count;     /* SM2解密次数 */
    uint64_t sm3_hash_count;        /* SM3哈希次数 */
    uint64_t sm4_encrypt_count;     /* SM4加密次数 */
    uint64_t sm4_decrypt_count;     /* SM4解密次数 */
    uint64_t total_bytes_processed; /* 处理的总字节数 */
    uint64_t total_operations;      /* 总操作次数 */
};

/**
 * 获取性能统计信息
 * @param stats 统计信息输出缓冲区
 * @return 成功返回0，失败返回负错误码
 */
int gmalg_get_perf_stats(struct gmalg_perf_stats *stats) GMALG_NONNULL(1);

/**
 * 重置性能统计信息
 */
void gmalg_reset_perf_stats(void);

/* 错误处理相关 */

/**
 * 获取最后一次错误的描述信息
 * @return 错误描述字符串
 */
const char* gmalg_get_last_error(void);

/**
 * 设置最后一次错误信息
 * @param error_msg 错误信息
 */
void gmalg_set_last_error(const char* error_msg);

/* 版本信息 */
#define GMALG_AF_ALG_VERSION_MAJOR 1
#define GMALG_AF_ALG_VERSION_MINOR 0
#define GMALG_AF_ALG_VERSION_PATCH 0

/**
 * 获取版本信息
 * @return 版本字符串
 */
const char* gmalg_get_version(void);

/**
 * 检查版本兼容性
 * @param major 主版本号
 * @param minor 次版本号
 * @return 兼容返回true，不兼容返回false
 */
bool gmalg_check_version(int major, int minor);

/* 初始化和清理 */

/**
 * 全局初始化函数
 * @return 成功返回0，失败返回负错误码
 */
int gmalg_global_init(void) GMALG_WARN_UNUSED_RESULT;

/**
 * 全局清理函数
 */
void gmalg_global_cleanup(void);

/* 配置相关 */

/**
 * GMALG配置结构
 */
struct gmalg_config {
    bool enable_debug;              /* 是否启用调试 */
    bool enable_perf_stats;         /* 是否启用性能统计 */
    size_t hash_buffer_size;        /* 哈希缓冲区大小 */
    int socket_timeout;             /* socket超时时间（秒） */
    bool strict_key_check;          /* 是否启用严格密钥检查 */
};

/**
 * 设置全局配置
 * @param config 配置结构指针
 * @return 成功返回0，失败返回负错误码
 */
int gmalg_set_config(const struct gmalg_config *config) GMALG_NONNULL(1);

/**
 * 获取当前配置
 * @param config 配置结构输出缓冲区
 * @return 成功返回0，失败返回负错误码
 */
int gmalg_get_config(struct gmalg_config *config) GMALG_NONNULL(1);

/* 测试和验证函数 */

/**
 * 运行内置的算法测试套件
 * @return 测试通过返回0，失败返回负错误码
 */
int gmalg_run_self_test(void) GMALG_WARN_UNUSED_RESULT;

/**
 * 验证AF_ALG内核接口可用性
 * @return 可用返回0，不可用返回负错误码
 */
int gmalg_check_kernel_support(void) GMALG_WARN_UNUSED_RESULT;

/**
 * 执行算法基准测试
 * @param algorithm 算法名称（"sm2", "sm3", "sm4"）
 * @param iterations 测试迭代次数
 * @param data_size 测试数据大小
 * @return 成功返回每秒操作数，失败返回负错误码
 */
double gmalg_benchmark(const char *algorithm, int iterations, size_t data_size) 
    GMALG_NONNULL(1);

#endif /* GMALG_AF_ALG_H_ */