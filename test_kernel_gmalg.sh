#!/bin/bash

# 测试内核中国密算法可用性的脚本

echo "检查内核中的国密算法支持..."

# 检查AF_ALG支持
echo "1. 检查AF_ALG支持:"
if [ -e /proc/crypto ]; then
    echo "   /proc/crypto 存在"
else
    echo "   错误：/proc/crypto 不存在，内核可能不支持crypto API"
    exit 1
fi

# 检查可用的算法
echo ""
echo "2. 检查可用的加密算法:"
echo "   可用的hash算法:"
grep "^name" /proc/crypto | grep -E "(sm3|SM3)" || echo "   未找到SM3算法"

echo "   可用的cipher算法:"
grep "^name" /proc/crypto | grep -E "(sm4|SM4)" || echo "   未找到SM4算法"

echo "   可用的akcipher算法:"
grep "^name" /proc/crypto | grep -E "(sm2|SM2)" || echo "   未找到SM2算法"

echo ""
echo "3. 详细的crypto信息:"
echo "   所有算法列表:"
cat /proc/crypto | grep "^name" | sort | uniq

echo ""
echo "4. 检查内核模块:"
echo "   已加载的crypto相关模块:"
lsmod | grep -i crypto || echo "   未找到crypto模块"
lsmod | grep -i sm || echo "   未找到SM相关模块"

echo ""
echo "5. 测试AF_ALG socket创建:"

# 创建一个简单的C程序来测试AF_ALG
cat > /tmp/test_af_alg.c << 'EOF'
#include <stdio.h>
#include <sys/socket.h>
#include <linux/if_alg.h>
#include <unistd.h>
#include <string.h>
#include <errno.h>

int test_algorithm(const char *type, const char *name) {
    struct sockaddr_alg sa = {
        .salg_family = AF_ALG,
    };
    int sockfd;
    
    strncpy((char *)sa.salg_type, type, sizeof(sa.salg_type) - 1);
    strncpy((char *)sa.salg_name, name, sizeof(sa.salg_name) - 1);
    
    sockfd = socket(AF_ALG, SOCK_SEQPACKET, 0);
    if (sockfd < 0) {
        printf("   创建AF_ALG socket失败: %s\n", strerror(errno));
        return -1;
    }
    
    if (bind(sockfd, (struct sockaddr *)&sa, sizeof(sa)) < 0) {
        printf("   绑定算法%s-%s失败: %s\n", type, name, strerror(errno));
        close(sockfd);
        return -1;
    }
    
    printf("   算法%s-%s测试成功\n", type, name);
    close(sockfd);
    return 0;
}

int main() {
    printf("   测试SM2算法:\n");
    test_algorithm("akcipher", "sm2");
    
    printf("   测试SM3算法:\n");
    test_algorithm("hash", "sm3");
    
    printf("   测试SM4算法:\n");
    test_algorithm("skcipher", "sm4");
    
    return 0;
}
EOF

# 编译并运行测试程序
gcc -o /tmp/test_af_alg /tmp/test_af_alg.c 2>/dev/null
if [ $? -eq 0 ]; then
    /tmp/test_af_alg
    rm -f /tmp/test_af_alg /tmp/test_af_alg.c
else
    echo "   编译测试程序失败，请检查gcc是否安装"
    rm -f /tmp/test_af_alg.c
fi

echo ""
echo "6. 建议:"
echo "   如果上述测试失败，请检查："
echo "   - 内核是否正确编译了国密算法模块"
echo "   - 相关内核模块是否已加载"
echo "   - 算法名称是否与内核中的名称匹配"
echo "   - AF_ALG接口是否在内核中启用"
