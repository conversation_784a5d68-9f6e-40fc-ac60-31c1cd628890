eap-peap {

    # Maximum size of an EAP-PEAP packet.
    # fragment_size = 1024

    # Include length in non-fragmented EAP-PEAP packets.
    # include_length = no

    # Whether to load the plugin. Can also be an integer to increase the
    # priority of this plugin.
    load = yes

    # Maximum number of processed EAP-PEAP packets (0 = no limit).
    # max_message_count = 32

    # Phase2 EAP client authentication method.
    # phase2_method = mschapv2

    # Phase2 EAP Identity request piggybacked by server onto TLS Finished
    # message.
    # phase2_piggyback = no

    # Start phase2 EAP TNC protocol after successful client authentication.
    # phase2_tnc = no

    # Request peer authentication based on a client certificate.
    # request_peer_auth = no

}

