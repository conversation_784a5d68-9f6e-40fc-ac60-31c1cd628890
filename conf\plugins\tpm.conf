tpm {

    # Is the TPM 2.0 FIPS-186-4 compliant, forcing e.g. the use of the default
    # salt length instead of maximum salt length with RSAPSS padding.
    # fips_186_4 = no

    # Whether to load the plugin. Can also be an integer to increase the
    # priority of this plugin.
    load = yes

    # Whether the TPM should be used as RNG.
    # use_rng = no

    tcti {

        # Name of TPM 2.0 TCTI library. Valid values: tabrmd, device or mssim.
        # Defaults are device if the /dev/tpmrm0 in-kernel TPM 2.0 resource
        # manager device exists, and tabrmd otherwise, requiring the d-bus based
        # TPM 2.0 access broker and resource manager to be available.
        # name = device|tabrmd

        # Options for the TPM 2.0 TCTI library. Defaults are /dev/tpmrm0 if the
        # TCTI library name is device and no options otherwise.
        # opts = /dev/tpmrm0|<none>

    }

}

