charon {

    # Section to define file loggers, see LOGGER CONFIGURATION in
    # strongswan.conf(5).
    filelog {

        # <name> may be the full path to the log file if it only contains
        # characters permitted in section names. Is ignored if path is
        # specified.
        # <name> {

            # Loglevel for a specific subsystem.
            # <subsystem> = <default>

            # If this option is enabled log entries are appended to the existing
            # file.
            # append = yes

            # Default loglevel.
            # default = 1

            # Enabling this option disables block buffering and enables line
            # buffering.
            # flush_line = no

            # Prefix each log entry with the connection name and a unique
            # numerical identifier for each IKE_SA.
            # ike_name = no

            # Add the log level of each message after the subsystem (e.g.
            # [IKE2]).
            # log_level = no

            # Optional path to the log file. Overrides the section name. Must be
            # used if the path contains characters that aren't allowed in
            # section names.
            # path =

            # Adds the milliseconds within the current second after the
            # timestamp (separated by a dot, so time_format should end with %S
            # or %T).
            # time_add_ms = no

            # Prefix each log entry with a timestamp. The option accepts a
            # format string as passed to strftime(3).
            # time_format =

        # }

    }

    # Section to define syslog loggers, see LOGGER CONFIGURATION in
    # strongswan.conf(5).
    syslog {

        # Identifier for use with openlog(3).
        # identifier =

        # <facility> is one of the supported syslog facilities, see LOGGER
        # CONFIGURATION in strongswan.conf(5).
        # <facility> {

            # Loglevel for a specific subsystem.
            # <subsystem> = <default>

            # Default loglevel.
            # default = 1

            # Prefix each log entry with the connection name and a unique
            # numerical identifier for each IKE_SA.
            # ike_name = no

            # Add the log level of each message after the subsystem (e.g.
            # [IKE2]).
            # log_level = no

        # }

    }

}

