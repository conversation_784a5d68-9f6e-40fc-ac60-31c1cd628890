#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <unistd.h>
#include <fcntl.h>
#include <stdint.h>

#include "sm2.h"
#include "debug.h"

u8 rA[ECC_NUMBITS] = {
	0xD4,0xDE,0x15,0x47,0x4D,0xB7,0x4D,0x06,0x49,0x1C,0x44,0x0D,0x30,0x5E,0x01,0x24,
	0x00,0x99,0x0F,0x3E,0x39,0x0C,0x7E,0x87,0x15,0x3C,0x12,0xDB,0x2E,0xA6,0x0B,0xB3
};

u8 rB[ECC_NUMBITS]= {
	0x7E,0x07,0x12,0x48,0x14,0xB3,0x09,0x48,0x91,0x25,0xEA,0xED,0x10,0x11,0x13,0x16,
	0x4E,0xBF,0x0F,0x34,0x58,0xC5,0xBD,0x88,0x33,0x5C,0x1F,0x9D,0x59,0x62,0x43,0xD6};

ecc_point pubkeyA = {
	.x = {	0x16,0x0E,0x12,0x89,0x7D,0xF4,0xED,0xB6,0x1D,0xD8,0x12,0xFE,0xB9,0x67,0x48,0xFB,
		0xD3,0xCC,0xF4,0xFF,0xE2,0x6A,0xA6,0xF6,0xDB,0x95,0x40,0xAF,0x49,0xC9,0x42,0x32},
	.y = {	0x4A,0x7D,0xAD,0x08,0xBB,0x9A,0x45,0x95,0x31,0x69,0x4B,0xEB,0x20,0xAA,0x48,0x9D,
		0x66,0x49,0x97,0x5E,0x1B,0xFC,0xF8,0xC4,0x74,0x1B,0x78,0xB4,0xB2,0x23,0x00,0x7F},
};

u8 prikeyA[ECC_NUMBITS] = {
	0x81,0xEB,0x26,0xE9,0x41,0xBB,0x5A,0xF1,0x6D,0xF1,0x16,0x49,0x5F,0x90,0x69,0x52,
	0x72,0xAE,0x2C,0xD6,0x3D,0x6C,0x4A,0xE1,0x67,0x84,0x18,0xBE,0x48,0x23,0x00,0x29
};

ecc_point pubkeyB = {
	.x = {0x6A,0xE8,0x48,0xC5,0x7C,0x53,0xC7,0xB1,0xB5,0xFA,0x99,0xEB,0x22,0x86,0xAF,0x07,
		0x8B,0xA6,0x4C,0x64,0x59,0x1B,0x8B,0x56,0x6F,0x73,0x57,0xD5,0x76,0xF1,0x6D,0xFB},
	.y = {0xEE,0x48,0x9D,0x77,0x16,0x21,0xA2,0x7B,0x36,0xC5,0xC7,0x99,0x20,0x62,0xE9,0xCD,
		0x09,0xA9,0x26,0x43,0x86,0xF3,0xFB,0xEA,0x54,0xDF,0xF6,0x93,0x05,0x62,0x1C,0x4D},

};

u8 prikeyB[ECC_NUMBITS] = {
	0x78,0x51,0x29,0x91,0x7D,0x45,0xA9,0xEA,0x54,0x37,0xA5,0x93,0x56,0xB8,0x23,0x38,
	0xEA,0xAD,0xDA,0x6C,0xEB,0x19,0x90,0x88,0xF1,0x4A,0xE1,0x0D,0xEF,0xA2,0x29,0xB5};

u8 IDa[16] = {
	0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38
};

u8 IDb[16] = {
	0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38
};

ecc_point RA;
ecc_point RB;
ecc_point V;

u8 K[64];
u32 klen = 16;

u8 ZA[32];
u8 ZB[32];
u8 SB[32];
u8 SA[32];

int main()
{
	int rc;

	printf("ecc test \n");

	sm3_z(IDa, 16, &pubkeyA, ZA);
	sm3_z(IDb, 16, &pubkeyB, ZB);

	//a
	rc = ECC_KeyEx_Init_I(rA, &RA);
	if (rc)
		printf(" key ex err\n");
	else
		printf(" key ex ok\n");
	///b
	rc = ECC_KeyEx_Re_I(rB, prikeyB, &RA, &pubkeyA, ZA, ZB, K, klen, &RB, &V, SB);
	if (rc)
		printf(" key ex err\n");
	else
		printf(" key ex ok\n");
	//a
	rc = ECC_KeyEx_Init_II(rA, prikeyA, &RA, &RB, &pubkeyB, ZA, ZB, SB, K, klen, SA);
	if (rc)
		printf(" key ex err\n");
	else
		printf(" key ex ok\n");
	//bb
	rc = ECC_KeyEx_Re_II(&V, &RA, &RB, ZA, ZB, SA);
	if (rc)
		printf(" key ex err\n");
	else
		printf(" key ex ok\n");

	return 0;
}
