manager {

    # Credential database URI for manager. If it contains a password, make sure
    # to adjust the permissions of the config file accordingly.
    # database =

    # Enable debugging in manager.
    # debug = no

    # Plugins to load in manager.
    # load =

    # FastCGI socket of manager, to run it statically.
    # socket =

    # Threads to use for request handling.
    # threads = 10

    # Session timeout for manager.
    # timeout = 15m

}

