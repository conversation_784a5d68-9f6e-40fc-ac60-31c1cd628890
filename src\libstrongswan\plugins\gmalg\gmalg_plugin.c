/*
 * Copyright (C) 2008-2016 <PERSON>
 * Copyright (C) 2008 <PERSON>
 * HSR Hochschule fuer Technik Rapperswil
 *
 * 更新版本：集成Linux内核AF_ALG接口支持
 * 提供高性能的国密算法实现
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License as published by the
 * Free Software Foundation; either version 2 of the License, or (at your
 * option) any later version.  See <http://www.fsf.org/copyleft/gpl.txt>.
 *
 * This program is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
 * for more details.
 */

#include <library.h>
#include <utils/debug.h>
#include <threading/thread.h>
#include <threading/mutex.h>
#include <threading/thread_value.h>
#include <sys/socket.h>
#include <linux/if_alg.h>
#include <errno.h>
#include <string.h>

#include "gmalg_plugin.h"
#include "gmalg.h"           /* 确保包含国密类型和函数声明 */
#include "gmalg_af_alg.h"      /* 新增：AF_ALG接口头文件 */
#include "gmalg_crypter.h"
#include "gmalg_hasher.h"
#include "gmalg_ec_private_key.h"
#include "gmalg_ec_public_key.h"
#include "gmalg_rng.h"
#include "gmalg_ec_diffie_hellman.h"

typedef struct private_gmalg_plugin_t private_gmalg_plugin_t;

/**
 * private data of gmalg_plugin
 */
struct private_gmalg_plugin_t {
    /**
     * public functions
     */
    gmalg_plugin_t public;
    
    /**
     * AF_ALG接口是否可用
     */
    bool af_alg_available;
    
    /**
     * 全局设备句柄
     */
    void *device_handle;
};

METHOD(plugin_t, get_name, char*,
    private_gmalg_plugin_t *this)
{
    return "gmalg";
}

/**
 * 检查AF_ALG接口可用性
 */
static bool check_af_alg_support()
{
    int fd;

    /* 首先检查AF_ALG基本支持 */
    fd = socket(AF_ALG, SOCK_SEQPACKET, 0);
    if (fd < 0) {
        DBG1(DBG_LIB, "内核不支持AF_ALG接口，错误: %s", strerror(errno));
        return FALSE;
    }
    close(fd);

    DBG1(DBG_LIB, "AF_ALG接口可用，正在检查国密算法支持...");

    /* 检查国密算法是否可用（非强制性） */
    int ret = GMALG_LibTest();
    if (ret != 0) {
        DBG1(DBG_LIB, "内核中的国密算法不完全可用，但插件仍将加载");
        DBG1(DBG_LIB, "请确保内核已编译并加载了SM2/SM3/SM4算法模块");
        /* 即使算法不完全可用，也允许插件加载，这样可以在日志中看到详细信息 */
    } else {
        DBG1(DBG_LIB, "GMALG AF_ALG接口完全可用，将使用内核高性能实现");
    }

    return TRUE;
}

/**
 * 初始化AF_ALG接口
 */
static bool init_af_alg_interface(private_gmalg_plugin_t *this)
{
    int ret;

    /* 初始化全局AF_ALG接口 */
    ret = gmalg_global_init();
    if (ret != 0) {
        DBG1(DBG_LIB, "GMALG全局初始化失败，但插件仍将加载");
        /* 允许插件加载，即使初始化失败 */
    }

    /* 尝试打开设备句柄 */
    ret = GMALG_OpenDevice(&this->device_handle);
    if (ret != 0) {
        DBG1(DBG_LIB, "打开GMALG设备失败，某些功能可能不可用");
        this->device_handle = NULL;
        /* 不返回失败，允许插件加载 */
    } else {
        DBG1(DBG_LIB, "GMALG设备打开成功");
    }

    DBG1(DBG_LIB, "GMALG插件初始化完成");
    return TRUE;
}

METHOD(plugin_t, get_features, int,
    private_gmalg_plugin_t *this, plugin_feature_t *features[])
{
    static plugin_feature_t f[] = {
        /* GmSdf threading callbacks */
        PLUGIN_PROVIDE(CUSTOM, "gmalg-threading"),
        
        /* 优先使用AF_ALG实现的crypters */
        PLUGIN_REGISTER(CRYPTER, gmalg_crypter_create),
            PLUGIN_PROVIDE(CRYPTER, ENCR_SM1_ECB, 16),
            PLUGIN_PROVIDE(CRYPTER, ENCR_SM1_CBC, 16),
            PLUGIN_PROVIDE(CRYPTER, ENCR_SM4_ECB, 16),
            PLUGIN_PROVIDE(CRYPTER, ENCR_SM4_CBC, 16),
            PLUGIN_PROVIDE(CRYPTER, ENCR_NULL, 0),
            
        /* 优先使用AF_ALG实现的hashers */
        PLUGIN_REGISTER(HASHER, gmalg_hasher_create),
            PLUGIN_PROVIDE(HASHER, HASH_SM3),
            
        /* SM2椭圆曲线DH groups */
        PLUGIN_REGISTER(DH, gmalg_ec_diffie_hellman_create),
            PLUGIN_PROVIDE(DH, CURVE_SM2),
            
        /* SM2私钥/公钥加载和生成 */
        PLUGIN_REGISTER(PRIVKEY, gmalg_ec_private_key_load, TRUE),
            PLUGIN_PROVIDE(PRIVKEY, KEY_SM2),
        PLUGIN_REGISTER(PRIVKEY_GEN, gmalg_ec_private_key_gen, FALSE),
            PLUGIN_PROVIDE(PRIVKEY_GEN, KEY_SM2),
        PLUGIN_REGISTER(PUBKEY, gmalg_ec_public_key_load, TRUE),
            PLUGIN_PROVIDE(PUBKEY, KEY_SM2),
            
        /* SM2签名和验证 */
        PLUGIN_PROVIDE(PRIVKEY_SIGN, SIGN_SM2_WITH_SM3),
        PLUGIN_PROVIDE(PUBKEY_VERIFY, SIGN_SM2_WITH_SM3),

        /* 随机数生成器 */
        PLUGIN_REGISTER(RNG, gmalg_rng_create),
            PLUGIN_PROVIDE(RNG, RNG_STRONG),
            PLUGIN_PROVIDE(RNG, RNG_WEAK),
    };
    
    *features = f;
    return countof(f);
}

METHOD(plugin_t, destroy, void,
    private_gmalg_plugin_t *this)
{
    if (this->af_alg_available && this->device_handle) {
        /* 关闭设备句柄 */
        GMALG_CloseDevice(this->device_handle);
        this->device_handle = NULL;
        
        /* 清理全局资源 */
        gmalg_global_cleanup();
        
        DBG1(DBG_LIB, "GMALG AF_ALG接口已清理");
    }
    
    free(this);
}

/*
 * see header file
 */
plugin_t *gmalg_plugin_create()
{
    private_gmalg_plugin_t *this;

    INIT(this,
        .public = {
            .plugin = {
                .get_name = _get_name,
                .get_features = _get_features,
                .destroy = _destroy,
            },
        },
        .af_alg_available = FALSE,
        .device_handle = NULL,
    );

    /* 检查AF_ALG支持并初始化 */
    DBG1(DBG_LIB, "正在初始化GMALG插件...");

    this->af_alg_available = check_af_alg_support();
    if (this->af_alg_available) {
        if (!init_af_alg_interface(this)) {
            this->af_alg_available = FALSE;
            DBG1(DBG_LIB, "AF_ALG接口初始化失败，但插件仍将加载");
        }
    } else {
        DBG1(DBG_LIB, "AF_ALG接口不可用，插件功能将受限");
        this->af_alg_available = FALSE;
    }

    /* 无论如何都加载插件，这样可以在日志中看到详细的错误信息 */
    DBG1(DBG_LIB, "GMALG插件已成功加载 (AF_ALG可用: %s)",
         this->af_alg_available ? "是" : "否");

    return &this->public.plugin;
}

/**
 * 获取全局设备句柄（供其他组件使用）
 */
void* gmalg_get_device_handle()
{
    /* 这里应该有一个全局的插件实例指针 */
    /* 为简化实现，暂时返回静态变量 */
    static void *global_handle = NULL;
    
    if (!global_handle) {
        GMALG_OpenDevice(&global_handle);
    }
    
    return global_handle;
}

/**
 * 检查AF_ALG是否可用（供其他组件查询）
 */
bool gmalg_is_af_alg_available()
{
    return check_af_alg_support();
}