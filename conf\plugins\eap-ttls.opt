charon.plugins.eap-ttls.fragment_size = 1024
	Maximum size of an EAP-TTLS packet.

charon.plugins.eap-ttls.max_message_count = 32
	Maximum number of processed EAP-TTLS packets (0 = no limit).

charon.plugins.eap-ttls.include_length = yes
	Include length in non-fragmented EAP-TTLS packets.

charon.plugins.eap-ttls.phase2_method = md5
	Phase2 EAP client authentication method.

charon.plugins.eap-ttls.phase2_piggyback = no
	Phase2 EAP Identity request piggybacked by server onto TLS Finished message.

charon.plugins.eap-ttls.phase2_tnc = no
	Start phase2 EAP TNC protocol after successful client authentication.

charon.plugins.eap-ttls.phase2_tnc_method = pt
	Phase2 EAP TNC transport protocol (_pt_ as IETF standard or legacy _tnc_)

charon.plugins.eap-ttls.request_peer_auth = no
	Request peer authentication based on a client certificate.
