.TH CHARON\-CMD 8 "2013-06-21" "@PACKAGE_VERSION@" "strongSwan"
.SH "NAME"
charon\-cmd \- Simple IKE client (IPsec VPN client)
.SH SYNOPSIS
.B charon\-cmd
.B \-\-host
.I hostname
.B \-\-identity
.I identity
.B [ options ]
.PP
.SH "DESCRIPTION"
.B charon\-cmd
is a program for setting up IPsec VPN connections using the Internet Key
Exchange protocol (IKE) in version 1 and 2.  It supports a number of different
road-warrior scenarios.
.PP
Like the IKE daemon
.BR charon ,
.B charon\-cmd
has to be run as
.B root
(or more specifically as a user with
.B CAP_NET_ADMIN
capability).
.PP
Of the following options at least
.I \-\-host
and
.I \-\-identity
are required. Depending on the selected authentication
.I profile
credentials also have to be provided with their respective options.
.PP
Many of the
.BR charon -specific
configuration options in
.I strongswan.conf
also apply to
.BR charon\-cmd .
For instance, to configure customized logging to
.B stdout
the following snippet can be used:
.PP
.EX
	charon-cmd {
		filelog {
			stdout {
				default = 1
				ike = 2
				cfg = 2
			}
		}
	}
.EE
.PP
.SH "OPTIONS"
.TP
.B "\-\-help"
Prints usage information and a short summary of the available options.
.TP
.B "\-\-version"
Prints the strongSwan version.
.TP
.BI "\-\-debug " level
Sets the default log level (defaults to 1).
.I level
is a number between -1 and 4.
Refer to
.I strongswan.conf
for options that allow a more fine-grained configuration of the logging
output.
.TP
.BI "\-\-host " hostname
DNS name or IP address to connect to.
.TP
.BI "\-\-identity " identity
Identity the client uses for the IKE exchange.
.TP
.BI "\-\-eap\-identity " identity
Identity the client uses for EAP authentication.
.TP
.BI "\-\-xauth\-username " username
Username the client uses for XAuth authentication.
.TP
.BI "\-\-remote\-identity " identity
Server identity to expect, defaults to
.IR hostname .
.TP
.BI "\-\-cert " path
Trusted certificate, either for authentication or trust chain validation.
To provide more than one certificate multiple
.B \-\-cert
options can be used.
.TP
.BI "\-\-rsa " path
RSA private key to use for authentication (if a password is required, it will
be requested on demand).
.TP
.BI "\-\-p12 " path
PKCS#12 file with private key and certificates to use for authentication and
trust chain validation (if a password is required it will be requested on
demand).
.TP
.RI "\fB\-\-agent\fR[=" socket ]
Use SSH agent for authentication. If
.I socket
is not specified it is read from the
.B SSH_AUTH_SOCK
environment variable.
.TP
.BI "\-\-local\-ts " subnet
Additional traffic selector to propose for our side, the requested virtual IP
address will always be proposed.
.TP
.BI "\-\-remote\-ts " subnet
Traffic selector to propose for remote side, defaults to 0.0.0.0/0.
.TP
.BI "\-\-ike\-proposal " proposal
IKE proposal to offer instead of default. For IKEv1, a single proposal consists
of one encryption algorithm, an integrity/PRF algorithm and a DH group. IKEv2
can propose multiple algorithms of the same kind. To specify multiple proposals,
repeat the option.
.TP
.BI "\-\-esp\-proposal " proposal
ESP proposal to offer instead of default. For IKEv1, a single proposal consists
of one encryption algorithm, an integrity algorithm and an optional DH group for
Perfect Forward Secrecy rekeying. IKEv2 can propose multiple algorithms of the
same kind. To specify multiple proposals, repeat the option.
.TP
.BI "\-\-ah\-proposal " proposal
AH proposal to offer instead of ESP. For IKEv1, a single proposal consists
of an integrity algorithm and an optional DH group for Perfect Forward Secrecy
rekeying. IKEv2 can propose multiple algorithms of the same kind. To specify
multiple proposals, repeat the option.
.TP
.BI "\-\-profile " name
Authentication profile to use, the list of supported profiles can be found
in the
.B Authentication Profiles
sections below. Defaults to
.B ikev2\-pub
if a private key was supplied, and to
.B ikev2\-eap
otherwise.
.PP
.SS "IKEv2 Authentication Profiles"
.TP
.B "ikev2\-pub"
IKEv2 with public key client and server authentication
.TP
.B "ikev2\-eap"
IKEv2 with EAP client authentication and public key server authentication
.TP
.B "ikev2\-pub\-eap"
IKEv2 with public key and EAP client authentication (RFC 4739) and public key
server authentication
.PP
.SS "IKEv1 Authentication Profiles"
The following authentication profiles use either Main Mode or Aggressive Mode,
the latter is denoted with a \fB\-am\fR suffix.
.TP
.BR "ikev1\-pub" ", " "ikev1\-pub\-am"
IKEv1 with public key client and server authentication
.TP
.BR "ikev1\-xauth" ", " "ikev1\-xauth\-am"
IKEv1 with public key client and server authentication, followed by client XAuth
authentication
.TP
.BR "ikev1\-xauth\-psk" ", " "ikev1\-xauth\-psk\-am"
IKEv1 with pre-shared key (PSK) client and server authentication, followed by
client XAuth authentication (INSECURE!)
.TP
.BR "ikev1\-hybrid" ", " "ikev1\-hybrid\-am"
IKEv1 with public key server authentication only, followed by client XAuth
authentication
.PP
.SH "SEE ALSO"
\fBstrongswan.conf\fR(5), \fBipsec\fR(8)
