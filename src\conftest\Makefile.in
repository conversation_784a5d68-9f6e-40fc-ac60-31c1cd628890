# Makefile.in generated by automake 1.16.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994-2018 Free Software Foundation, Inc.

# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

VPATH = @srcdir@
am__is_gnu_make = { \
  if test -z '$(MAKELEVEL)'; then \
    false; \
  elif test -n '$(MAKE_HOST)'; then \
    true; \
  elif test -n '$(MAKE_VERSION)' && test -n '$(CURDIR)'; then \
    true; \
  else \
    false; \
  fi; \
}
am__make_running_with_option = \
  case $${target_option-} in \
      ?) ;; \
      *) echo "am__make_running_with_option: internal error: invalid" \
              "target option '$${target_option-}' specified" >&2; \
         exit 1;; \
  esac; \
  has_opt=no; \
  sane_makeflags=$$MAKEFLAGS; \
  if $(am__is_gnu_make); then \
    sane_makeflags=$$MFLAGS; \
  else \
    case $$MAKEFLAGS in \
      *\\[\ \	]*) \
        bs=\\; \
        sane_makeflags=`printf '%s\n' "$$MAKEFLAGS" \
          | sed "s/$$bs$$bs[$$bs $$bs	]*//g"`;; \
    esac; \
  fi; \
  skip_next=no; \
  strip_trailopt () \
  { \
    flg=`printf '%s\n' "$$flg" | sed "s/$$1.*$$//"`; \
  }; \
  for flg in $$sane_makeflags; do \
    test $$skip_next = yes && { skip_next=no; continue; }; \
    case $$flg in \
      *=*|--*) continue;; \
        -*I) strip_trailopt 'I'; skip_next=yes;; \
      -*I?*) strip_trailopt 'I';; \
        -*O) strip_trailopt 'O'; skip_next=yes;; \
      -*O?*) strip_trailopt 'O';; \
        -*l) strip_trailopt 'l'; skip_next=yes;; \
      -*l?*) strip_trailopt 'l';; \
      -[dEDm]) skip_next=yes;; \
      -[JT]) skip_next=yes;; \
    esac; \
    case $$flg in \
      *$$target_option*) has_opt=yes; break;; \
    esac; \
  done; \
  test $$has_opt = yes
am__make_dryrun = (target_option=n; $(am__make_running_with_option))
am__make_keepgoing = (target_option=k; $(am__make_running_with_option))
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
ipsec_PROGRAMS = conftest$(EXEEXT)
subdir = src/conftest
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps = $(top_srcdir)/m4/config/libtool.m4 \
	$(top_srcdir)/m4/config/ltoptions.m4 \
	$(top_srcdir)/m4/config/ltsugar.m4 \
	$(top_srcdir)/m4/config/ltversion.m4 \
	$(top_srcdir)/m4/config/lt~obsolete.m4 \
	$(top_srcdir)/m4/macros/split-package-version.m4 \
	$(top_srcdir)/m4/macros/with.m4 \
	$(top_srcdir)/m4/macros/enable-disable.m4 \
	$(top_srcdir)/m4/macros/add-plugin.m4 \
	$(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
DIST_COMMON = $(srcdir)/Makefile.am $(am__DIST_COMMON)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__installdirs = "$(DESTDIR)$(ipsecdir)"
PROGRAMS = $(ipsec_PROGRAMS)
am__dirstamp = $(am__leading_dot)dirstamp
am_conftest_OBJECTS = conftest.$(OBJEXT) config.$(OBJEXT) \
	actions.$(OBJEXT) hooks/ike_auth_fill.$(OBJEXT) \
	hooks/unsort_message.$(OBJEXT) hooks/add_notify.$(OBJEXT) \
	hooks/unencrypted_notify.$(OBJEXT) \
	hooks/ignore_message.$(OBJEXT) hooks/add_payload.$(OBJEXT) \
	hooks/set_critical.$(OBJEXT) hooks/force_cookie.$(OBJEXT) \
	hooks/set_ike_version.$(OBJEXT) hooks/pretend_auth.$(OBJEXT) \
	hooks/set_length.$(OBJEXT) hooks/log_proposals.$(OBJEXT) \
	hooks/set_proposal_number.$(OBJEXT) hooks/log_ke.$(OBJEXT) \
	hooks/log_id.$(OBJEXT) hooks/custom_proposal.$(OBJEXT) \
	hooks/set_ike_spi.$(OBJEXT) hooks/set_ike_request.$(OBJEXT) \
	hooks/set_reserved.$(OBJEXT) hooks/set_ike_initiator.$(OBJEXT) \
	hooks/log_ts.$(OBJEXT) hooks/rebuild_auth.$(OBJEXT) \
	hooks/reset_seq.$(OBJEXT)
conftest_OBJECTS = $(am_conftest_OBJECTS)
am__DEPENDENCIES_1 =
conftest_DEPENDENCIES =  \
	$(top_builddir)/src/libstrongswan/libstrongswan.la \
	$(top_builddir)/src/libcharon/libcharon.la \
	$(am__DEPENDENCIES_1) $(am__DEPENDENCIES_1) \
	$(am__DEPENDENCIES_1)
AM_V_lt = $(am__v_lt_@AM_V@)
am__v_lt_ = $(am__v_lt_@AM_DEFAULT_V@)
am__v_lt_0 = --silent
am__v_lt_1 = 
AM_V_P = $(am__v_P_@AM_V@)
am__v_P_ = $(am__v_P_@AM_DEFAULT_V@)
am__v_P_0 = false
am__v_P_1 = :
AM_V_GEN = $(am__v_GEN_@AM_V@)
am__v_GEN_ = $(am__v_GEN_@AM_DEFAULT_V@)
am__v_GEN_0 = @echo "  GEN     " $@;
am__v_GEN_1 = 
AM_V_at = $(am__v_at_@AM_V@)
am__v_at_ = $(am__v_at_@AM_DEFAULT_V@)
am__v_at_0 = @
am__v_at_1 = 
DEFAULT_INCLUDES = -I.@am__isrc@ -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/depcomp
am__maybe_remake_depfiles = depfiles
am__depfiles_remade = ./$(DEPDIR)/actions.Po ./$(DEPDIR)/config.Po \
	./$(DEPDIR)/conftest.Po hooks/$(DEPDIR)/add_notify.Po \
	hooks/$(DEPDIR)/add_payload.Po \
	hooks/$(DEPDIR)/custom_proposal.Po \
	hooks/$(DEPDIR)/force_cookie.Po \
	hooks/$(DEPDIR)/ignore_message.Po \
	hooks/$(DEPDIR)/ike_auth_fill.Po hooks/$(DEPDIR)/log_id.Po \
	hooks/$(DEPDIR)/log_ke.Po hooks/$(DEPDIR)/log_proposals.Po \
	hooks/$(DEPDIR)/log_ts.Po hooks/$(DEPDIR)/pretend_auth.Po \
	hooks/$(DEPDIR)/rebuild_auth.Po hooks/$(DEPDIR)/reset_seq.Po \
	hooks/$(DEPDIR)/set_critical.Po \
	hooks/$(DEPDIR)/set_ike_initiator.Po \
	hooks/$(DEPDIR)/set_ike_request.Po \
	hooks/$(DEPDIR)/set_ike_spi.Po \
	hooks/$(DEPDIR)/set_ike_version.Po \
	hooks/$(DEPDIR)/set_length.Po \
	hooks/$(DEPDIR)/set_proposal_number.Po \
	hooks/$(DEPDIR)/set_reserved.Po \
	hooks/$(DEPDIR)/unencrypted_notify.Po \
	hooks/$(DEPDIR)/unsort_message.Po
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
LTCOMPILE = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=compile $(CC) $(DEFS) \
	$(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) \
	$(AM_CFLAGS) $(CFLAGS)
AM_V_CC = $(am__v_CC_@AM_V@)
am__v_CC_ = $(am__v_CC_@AM_DEFAULT_V@)
am__v_CC_0 = @echo "  CC      " $@;
am__v_CC_1 = 
CCLD = $(CC)
LINK = $(LIBTOOL) $(AM_V_lt) --tag=CC $(AM_LIBTOOLFLAGS) \
	$(LIBTOOLFLAGS) --mode=link $(CCLD) $(AM_CFLAGS) $(CFLAGS) \
	$(AM_LDFLAGS) $(LDFLAGS) -o $@
AM_V_CCLD = $(am__v_CCLD_@AM_V@)
am__v_CCLD_ = $(am__v_CCLD_@AM_DEFAULT_V@)
am__v_CCLD_0 = @echo "  CCLD    " $@;
am__v_CCLD_1 = 
SOURCES = $(conftest_SOURCES)
DIST_SOURCES = $(conftest_SOURCES)
am__can_run_installinfo = \
  case $$AM_UPDATE_INFO_DIR in \
    n|no|NO) false;; \
    *) (install-info --version) >/dev/null 2>&1;; \
  esac
am__tagged_files = $(HEADERS) $(SOURCES) $(TAGS_FILES) $(LISP)
# Read a list of newline-separated strings from the standard input,
# and print each of them once, without duplicates.  Input order is
# *not* preserved.
am__uniquify_input = $(AWK) '\
  BEGIN { nonempty = 0; } \
  { items[$$0] = 1; nonempty = 1; } \
  END { if (nonempty) { for (i in items) print i; }; } \
'
# Make sure the list of sources is unique.  This is necessary because,
# e.g., the same source file might be shared among _SOURCES variables
# for different programs/libraries.
am__define_uniq_tagged_files = \
  list='$(am__tagged_files)'; \
  unique=`for i in $$list; do \
    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
  done | $(am__uniquify_input)`
ETAGS = etags
CTAGS = ctags
am__DIST_COMMON = $(srcdir)/Makefile.in $(top_srcdir)/depcomp README
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
ALLOCA = @ALLOCA@
AMTAR = @AMTAR@
AM_DEFAULT_VERBOSITY = @AM_DEFAULT_VERBOSITY@
AR = @AR@
ATOMICLIB = @ATOMICLIB@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
BFDLIB = @BFDLIB@
BTLIB = @BTLIB@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
COVERAGE_CFLAGS = @COVERAGE_CFLAGS@
COVERAGE_LDFLAGS = @COVERAGE_LDFLAGS@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
DLLIB = @DLLIB@
DLLTOOL = @DLLTOOL@
DSYMUTIL = @DSYMUTIL@
DUMPBIN = @DUMPBIN@
EASY_INSTALL = @EASY_INSTALL@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
FGREP = @FGREP@
FUZZING_CFLAGS = @FUZZING_CFLAGS@
FUZZING_LDFLAGS = @FUZZING_LDFLAGS@
GEM = @GEM@
GENHTML = @GENHTML@
GIT_VERSION = @GIT_VERSION@
GPERF = @GPERF@
GPERF_LEN_TYPE = @GPERF_LEN_TYPE@
GPRBUILD = @GPRBUILD@
GREP = @GREP@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
LCOV = @LCOV@
LD = @LD@
LDFLAGS = @LDFLAGS@
LEX = @LEX@
LEXLIB = @LEXLIB@
LEX_OUTPUT_ROOT = @LEX_OUTPUT_ROOT@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LIBTOOL = @LIBTOOL@
LIPO = @LIPO@
LN_S = @LN_S@
LTLIBOBJS = @LTLIBOBJS@
LT_SYS_LIBRARY_PATH = @LT_SYS_LIBRARY_PATH@
MAKEINFO = @MAKEINFO@
MANIFEST_TOOL = @MANIFEST_TOOL@
MKDIR_P = @MKDIR_P@
MYSQLCFLAG = @MYSQLCFLAG@
MYSQLCONFIG = @MYSQLCONFIG@
MYSQLLIB = @MYSQLLIB@
NM = @NM@
NMEDIT = @NMEDIT@
OBJDUMP = @OBJDUMP@
OBJEXT = @OBJEXT@
OPENSSL_LIB = @OPENSSL_LIB@
OTOOL = @OTOOL@
OTOOL64 = @OTOOL64@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_URL = @PACKAGE_URL@
PACKAGE_VERSION = @PACKAGE_VERSION@
PACKAGE_VERSION_BUILD = @PACKAGE_VERSION_BUILD@
PACKAGE_VERSION_MAJOR = @PACKAGE_VERSION_MAJOR@
PACKAGE_VERSION_MINOR = @PACKAGE_VERSION_MINOR@
PACKAGE_VERSION_REVIEW = @PACKAGE_VERSION_REVIEW@
PATH_SEPARATOR = @PATH_SEPARATOR@
PERL = @PERL@
PKG_CONFIG = @PKG_CONFIG@
PKG_CONFIG_LIBDIR = @PKG_CONFIG_LIBDIR@
PKG_CONFIG_PATH = @PKG_CONFIG_PATH@
PLUGIN_CFLAGS = @PLUGIN_CFLAGS@
PTHREADLIB = @PTHREADLIB@
PYTHON = @PYTHON@
PYTHONEGGINSTALLDIR = @PYTHONEGGINSTALLDIR@
PYTHON_EXEC_PREFIX = @PYTHON_EXEC_PREFIX@
PYTHON_PACKAGE_VERSION = @PYTHON_PACKAGE_VERSION@
PYTHON_PLATFORM = @PYTHON_PLATFORM@
PYTHON_PREFIX = @PYTHON_PREFIX@
PYTHON_VERSION = @PYTHON_VERSION@
PY_TEST = @PY_TEST@
RANLIB = @RANLIB@
RTLIB = @RTLIB@
RUBYGEMDIR = @RUBYGEMDIR@
SED = @SED@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
SOCKLIB = @SOCKLIB@
STRIP = @STRIP@
TOX = @TOX@
UNWINDLIB = @UNWINDLIB@
VERSION = @VERSION@
YACC = @YACC@
YFLAGS = @YFLAGS@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_AR = @ac_ct_AR@
ac_ct_CC = @ac_ct_CC@
ac_ct_DUMPBIN = @ac_ct_DUMPBIN@
aikgen_plugins = @aikgen_plugins@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
attest_plugins = @attest_plugins@
bindir = @bindir@
botan_CFLAGS = @botan_CFLAGS@
botan_LIBS = @botan_LIBS@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
c_plugins = @c_plugins@
charon_natt_port = @charon_natt_port@
charon_plugins = @charon_plugins@
charon_udp_port = @charon_udp_port@
clearsilver_LIBS = @clearsilver_LIBS@
cmd_plugins = @cmd_plugins@
datadir = @datadir@
datarootdir = @datarootdir@
dbusdatadir = @dbusdatadir@
dbuspolicydir = @dbuspolicydir@
dev_headers = @dev_headers@
docdir = @docdir@
dvidir = @dvidir@
exec_prefix = @exec_prefix@
fips_mode = @fips_mode@
fuzz_plugins = @fuzz_plugins@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
imcvdir = @imcvdir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
ipsec_script = @ipsec_script@
ipsec_script_upper = @ipsec_script_upper@
ipsecdir = @ipsecdir@
ipsecgroup = @ipsecgroup@
ipseclibdir = @ipseclibdir@
ipsecuser = @ipsecuser@
json_CFLAGS = @json_CFLAGS@
json_LIBS = @json_LIBS@
libdir = @libdir@
libexecdir = @libexecdir@
libfuzzer = @libfuzzer@
libiptc_CFLAGS = @libiptc_CFLAGS@
libiptc_LIBS = @libiptc_LIBS@
linux_headers = @linux_headers@
localedir = @localedir@
localstatedir = @localstatedir@
manager_plugins = @manager_plugins@
mandir = @mandir@
medsrv_plugins = @medsrv_plugins@
mkdir_p = @mkdir_p@
nm_CFLAGS = @nm_CFLAGS@
nm_LIBS = @nm_LIBS@
nm_ca_dir = @nm_ca_dir@
nm_plugins = @nm_plugins@
oldincludedir = @oldincludedir@
p_plugins = @p_plugins@
pcsclite_CFLAGS = @pcsclite_CFLAGS@
pcsclite_LIBS = @pcsclite_LIBS@
pdfdir = @pdfdir@
piddir = @piddir@
pkgpyexecdir = @pkgpyexecdir@
pkgpythondir = @pkgpythondir@
pki_plugins = @pki_plugins@
plugindir = @plugindir@
pool_plugins = @pool_plugins@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
pyexecdir = @pyexecdir@
pythondir = @pythondir@
random_device = @random_device@
resolv_conf = @resolv_conf@
routing_table = @routing_table@
routing_table_prio = @routing_table_prio@
runstatedir = @runstatedir@
s_plugins = @s_plugins@
sbindir = @sbindir@
scepclient_plugins = @scepclient_plugins@
scripts_plugins = @scripts_plugins@
sharedstatedir = @sharedstatedir@
soup_CFLAGS = @soup_CFLAGS@
soup_LIBS = @soup_LIBS@
srcdir = @srcdir@
starter_plugins = @starter_plugins@
strongswan_conf = @strongswan_conf@
strongswan_options = @strongswan_options@
swanctldir = @swanctldir@
sysconfdir = @sysconfdir@
systemd_CFLAGS = @systemd_CFLAGS@
systemd_LIBS = @systemd_LIBS@
systemd_daemon_CFLAGS = @systemd_daemon_CFLAGS@
systemd_daemon_LIBS = @systemd_daemon_LIBS@
systemd_journal_CFLAGS = @systemd_journal_CFLAGS@
systemd_journal_LIBS = @systemd_journal_LIBS@
systemdsystemunitdir = @systemdsystemunitdir@
t_plugins = @t_plugins@
target_alias = @target_alias@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
tss2_CFLAGS = @tss2_CFLAGS@
tss2_LIBS = @tss2_LIBS@
tss2_esys_CFLAGS = @tss2_esys_CFLAGS@
tss2_esys_LIBS = @tss2_esys_LIBS@
tss2_socket_CFLAGS = @tss2_socket_CFLAGS@
tss2_socket_LIBS = @tss2_socket_LIBS@
tss2_sys_CFLAGS = @tss2_sys_CFLAGS@
tss2_sys_LIBS = @tss2_sys_LIBS@
tss2_tabrmd_CFLAGS = @tss2_tabrmd_CFLAGS@
tss2_tabrmd_LIBS = @tss2_tabrmd_LIBS@
urandom_device = @urandom_device@
wolfssl_CFLAGS = @wolfssl_CFLAGS@
wolfssl_LIBS = @wolfssl_LIBS@
xml_CFLAGS = @xml_CFLAGS@
xml_LIBS = @xml_LIBS@
AM_CPPFLAGS = \
	-I$(top_srcdir)/src/libstrongswan \
	-I$(top_srcdir)/src/libcharon \
	-DPLUGINS=\""${charon_plugins}\""

AM_CFLAGS = $(PLUGIN_CFLAGS)
conftest_SOURCES = conftest.c conftest.h config.c config.h actions.c actions.h \
	hooks/hook.h hooks/ike_auth_fill.c hooks/unsort_message.c \
	hooks/add_notify.c hooks/unencrypted_notify.c hooks/ignore_message.c \
	hooks/add_payload.c hooks/set_critical.c hooks/force_cookie.c \
	hooks/set_ike_version.c hooks/pretend_auth.c hooks/set_length.c \
	hooks/log_proposals.c hooks/set_proposal_number.c hooks/log_ke.c \
	hooks/log_id.c hooks/custom_proposal.c hooks/set_ike_spi.c \
	hooks/set_ike_request.c hooks/set_reserved.c hooks/set_ike_initiator.c \
	hooks/log_ts.c hooks/rebuild_auth.c hooks/reset_seq.c

conftest_LDADD = \
	$(top_builddir)/src/libstrongswan/libstrongswan.la \
	$(top_builddir)/src/libcharon/libcharon.la \
	-lm $(PTHREADLIB) $(ATOMICLIB) $(DLLIB)

EXTRA_DIST = README
all: all-am

.SUFFIXES:
.SUFFIXES: .c .lo .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu src/conftest/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu src/conftest/Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__maybe_remake_depfiles);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
install-ipsecPROGRAMS: $(ipsec_PROGRAMS)
	@$(NORMAL_INSTALL)
	@list='$(ipsec_PROGRAMS)'; test -n "$(ipsecdir)" || list=; \
	if test -n "$$list"; then \
	  echo " $(MKDIR_P) '$(DESTDIR)$(ipsecdir)'"; \
	  $(MKDIR_P) "$(DESTDIR)$(ipsecdir)" || exit 1; \
	fi; \
	for p in $$list; do echo "$$p $$p"; done | \
	sed 's/$(EXEEXT)$$//' | \
	while read p p1; do if test -f $$p \
	 || test -f $$p1 \
	  ; then echo "$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n;h' \
	    -e 's|.*|.|' \
	    -e 'p;x;s,.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/' | \
	sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1 } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) files[d] = files[d] " " $$1; \
	    else { print "f", $$3 "/" $$4, $$1; } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	    if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	    test -z "$$files" || { \
	    echo " $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files '$(DESTDIR)$(ipsecdir)$$dir'"; \
	    $(INSTALL_PROGRAM_ENV) $(LIBTOOL) $(AM_LIBTOOLFLAGS) $(LIBTOOLFLAGS) --mode=install $(INSTALL_PROGRAM) $$files "$(DESTDIR)$(ipsecdir)$$dir" || exit $$?; \
	    } \
	; done

uninstall-ipsecPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(ipsec_PROGRAMS)'; test -n "$(ipsecdir)" || list=; \
	files=`for p in $$list; do echo "$$p"; done | \
	  sed -e 'h;s,^.*/,,;s/$(EXEEXT)$$//;$(transform)' \
	      -e 's/$$/$(EXEEXT)/' \
	`; \
	test -n "$$list" || exit 0; \
	echo " ( cd '$(DESTDIR)$(ipsecdir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(ipsecdir)" && rm -f $$files

clean-ipsecPROGRAMS:
	@list='$(ipsec_PROGRAMS)'; test -n "$$list" || exit 0; \
	echo " rm -f" $$list; \
	rm -f $$list || exit $$?; \
	test -n "$(EXEEXT)" || exit 0; \
	list=`for p in $$list; do echo "$$p"; done | sed 's/$(EXEEXT)$$//'`; \
	echo " rm -f" $$list; \
	rm -f $$list
hooks/$(am__dirstamp):
	@$(MKDIR_P) hooks
	@: > hooks/$(am__dirstamp)
hooks/$(DEPDIR)/$(am__dirstamp):
	@$(MKDIR_P) hooks/$(DEPDIR)
	@: > hooks/$(DEPDIR)/$(am__dirstamp)
hooks/ike_auth_fill.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/unsort_message.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/add_notify.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/unencrypted_notify.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/ignore_message.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/add_payload.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/set_critical.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/force_cookie.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/set_ike_version.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/pretend_auth.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/set_length.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/log_proposals.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/set_proposal_number.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/log_ke.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/log_id.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/custom_proposal.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/set_ike_spi.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/set_ike_request.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/set_reserved.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/set_ike_initiator.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/log_ts.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/rebuild_auth.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)
hooks/reset_seq.$(OBJEXT): hooks/$(am__dirstamp) \
	hooks/$(DEPDIR)/$(am__dirstamp)

conftest$(EXEEXT): $(conftest_OBJECTS) $(conftest_DEPENDENCIES) $(EXTRA_conftest_DEPENDENCIES) 
	@rm -f conftest$(EXEEXT)
	$(AM_V_CCLD)$(LINK) $(conftest_OBJECTS) $(conftest_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)
	-rm -f hooks/*.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/actions.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/config.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/conftest.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/add_notify.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/add_payload.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/custom_proposal.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/force_cookie.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/ignore_message.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/ike_auth_fill.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/log_id.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/log_ke.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/log_proposals.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/log_ts.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/pretend_auth.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/rebuild_auth.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/reset_seq.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/set_critical.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/set_ike_initiator.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/set_ike_request.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/set_ike_spi.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/set_ike_version.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/set_length.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/set_proposal_number.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/set_reserved.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/unencrypted_notify.Po@am__quote@ # am--include-marker
@AMDEP_TRUE@@am__include@ @am__quote@hooks/$(DEPDIR)/unsort_message.Po@am__quote@ # am--include-marker

$(am__depfiles_remade):
	@$(MKDIR_P) $(@D)
	@echo '# dummy' >$@-t && $(am__mv) $@-t $@

am--depfiles: $(am__depfiles_remade)

.c.o:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.o$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ $<

.c.obj:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.obj$$||'`;\
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ `$(CYGPATH_W) '$<'` &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(COMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.c.lo:
@am__fastdepCC_TRUE@	$(AM_V_CC)depbase=`echo $@ | sed 's|[^/]*$$|$(DEPDIR)/&|;s|\.lo$$||'`;\
@am__fastdepCC_TRUE@	$(LTCOMPILE) -MT $@ -MD -MP -MF $$depbase.Tpo -c -o $@ $< &&\
@am__fastdepCC_TRUE@	$(am__mv) $$depbase.Tpo $$depbase.Plo
@AMDEP_TRUE@@am__fastdepCC_FALSE@	$(AM_V_CC)source='$<' object='$@' libtool=yes @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(AM_V_CC@am__nodep@)$(LTCOMPILE) -c -o $@ $<

mostlyclean-libtool:
	-rm -f *.lo

clean-libtool:
	-rm -rf .libs _libs

ID: $(am__tagged_files)
	$(am__define_uniq_tagged_files); mkid -fID $$unique
tags: tags-am
TAGS: tags

tags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	set x; \
	here=`pwd`; \
	$(am__define_uniq_tagged_files); \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: ctags-am

CTAGS: ctags
ctags-am: $(TAGS_DEPENDENCIES) $(am__tagged_files)
	$(am__define_uniq_tagged_files); \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"
cscopelist: cscopelist-am

cscopelist-am: $(am__tagged_files)
	list='$(am__tagged_files)'; \
	case "$(srcdir)" in \
	  [\\/]* | ?:[\\/]*) sdir="$(srcdir)" ;; \
	  *) sdir=$(subdir)/$(srcdir) ;; \
	esac; \
	for i in $$list; do \
	  if test -f "$$i"; then \
	    echo "$(subdir)/$$i"; \
	  else \
	    echo "$$sdir/$$i"; \
	  fi; \
	done >> $(top_builddir)/cscope.files

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) distdir-am

distdir-am: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: check-am
all-am: Makefile $(PROGRAMS)
installdirs:
	for dir in "$(DESTDIR)$(ipsecdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	if test -z '$(STRIP)'; then \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	      install; \
	else \
	  $(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	    install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	    "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'" install; \
	fi
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-rm -f hooks/$(DEPDIR)/$(am__dirstamp)
	-rm -f hooks/$(am__dirstamp)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-generic clean-ipsecPROGRAMS clean-libtool \
	mostlyclean-am

distclean: distclean-am
		-rm -f ./$(DEPDIR)/actions.Po
	-rm -f ./$(DEPDIR)/config.Po
	-rm -f ./$(DEPDIR)/conftest.Po
	-rm -f hooks/$(DEPDIR)/add_notify.Po
	-rm -f hooks/$(DEPDIR)/add_payload.Po
	-rm -f hooks/$(DEPDIR)/custom_proposal.Po
	-rm -f hooks/$(DEPDIR)/force_cookie.Po
	-rm -f hooks/$(DEPDIR)/ignore_message.Po
	-rm -f hooks/$(DEPDIR)/ike_auth_fill.Po
	-rm -f hooks/$(DEPDIR)/log_id.Po
	-rm -f hooks/$(DEPDIR)/log_ke.Po
	-rm -f hooks/$(DEPDIR)/log_proposals.Po
	-rm -f hooks/$(DEPDIR)/log_ts.Po
	-rm -f hooks/$(DEPDIR)/pretend_auth.Po
	-rm -f hooks/$(DEPDIR)/rebuild_auth.Po
	-rm -f hooks/$(DEPDIR)/reset_seq.Po
	-rm -f hooks/$(DEPDIR)/set_critical.Po
	-rm -f hooks/$(DEPDIR)/set_ike_initiator.Po
	-rm -f hooks/$(DEPDIR)/set_ike_request.Po
	-rm -f hooks/$(DEPDIR)/set_ike_spi.Po
	-rm -f hooks/$(DEPDIR)/set_ike_version.Po
	-rm -f hooks/$(DEPDIR)/set_length.Po
	-rm -f hooks/$(DEPDIR)/set_proposal_number.Po
	-rm -f hooks/$(DEPDIR)/set_reserved.Po
	-rm -f hooks/$(DEPDIR)/unencrypted_notify.Po
	-rm -f hooks/$(DEPDIR)/unsort_message.Po
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-ipsecPROGRAMS

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
		-rm -f ./$(DEPDIR)/actions.Po
	-rm -f ./$(DEPDIR)/config.Po
	-rm -f ./$(DEPDIR)/conftest.Po
	-rm -f hooks/$(DEPDIR)/add_notify.Po
	-rm -f hooks/$(DEPDIR)/add_payload.Po
	-rm -f hooks/$(DEPDIR)/custom_proposal.Po
	-rm -f hooks/$(DEPDIR)/force_cookie.Po
	-rm -f hooks/$(DEPDIR)/ignore_message.Po
	-rm -f hooks/$(DEPDIR)/ike_auth_fill.Po
	-rm -f hooks/$(DEPDIR)/log_id.Po
	-rm -f hooks/$(DEPDIR)/log_ke.Po
	-rm -f hooks/$(DEPDIR)/log_proposals.Po
	-rm -f hooks/$(DEPDIR)/log_ts.Po
	-rm -f hooks/$(DEPDIR)/pretend_auth.Po
	-rm -f hooks/$(DEPDIR)/rebuild_auth.Po
	-rm -f hooks/$(DEPDIR)/reset_seq.Po
	-rm -f hooks/$(DEPDIR)/set_critical.Po
	-rm -f hooks/$(DEPDIR)/set_ike_initiator.Po
	-rm -f hooks/$(DEPDIR)/set_ike_request.Po
	-rm -f hooks/$(DEPDIR)/set_ike_spi.Po
	-rm -f hooks/$(DEPDIR)/set_ike_version.Po
	-rm -f hooks/$(DEPDIR)/set_length.Po
	-rm -f hooks/$(DEPDIR)/set_proposal_number.Po
	-rm -f hooks/$(DEPDIR)/set_reserved.Po
	-rm -f hooks/$(DEPDIR)/unencrypted_notify.Po
	-rm -f hooks/$(DEPDIR)/unsort_message.Po
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic \
	mostlyclean-libtool

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-ipsecPROGRAMS

.MAKE: install-am install-strip

.PHONY: CTAGS GTAGS TAGS all all-am am--depfiles check check-am clean \
	clean-generic clean-ipsecPROGRAMS clean-libtool cscopelist-am \
	ctags ctags-am distclean distclean-compile distclean-generic \
	distclean-libtool distclean-tags distdir dvi dvi-am html \
	html-am info info-am install install-am install-data \
	install-data-am install-dvi install-dvi-am install-exec \
	install-exec-am install-html install-html-am install-info \
	install-info-am install-ipsecPROGRAMS install-man install-pdf \
	install-pdf-am install-ps install-ps-am install-strip \
	installcheck installcheck-am installdirs maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic mostlyclean-libtool pdf pdf-am ps ps-am \
	tags tags-am uninstall uninstall-am uninstall-ipsecPROGRAMS

.PRECIOUS: Makefile


# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:
