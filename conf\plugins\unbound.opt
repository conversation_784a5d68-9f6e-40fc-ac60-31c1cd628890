charon.plugins.unbound.resolv_conf = /etc/resolv.conf
	File to read DNS resolver configuration from.

charon.plugins.unbound.trust_anchors = /etc/ipsec.d/dnssec.keys
	File to read DNSSEC trust anchors from (usually root zone KSK).

	File to read DNSSEC trust anchors from (usually root zone KSK). The format
	of the file is the standard DNS Zone file format, anchors can be stored as
	DS or DNSKEY entries in the file.

charon.plugins.unbound.dlv_anchors =
	File to read trusted keys for DLV (DNSSEC Lookaside Validation) from.

	File to read trusted keys for DLV (DNSSEC Lookaside Validation) from. It
	uses the same format as _trust_anchors_. Only one DLV can be configured,
	which is then used as a root trusted DLV, this means that it is a lookaside
	for the root.
