charon.plugins.resolve.file = /etc/resolv.conf
	File where to add DNS server entries.

charon.plugins.resolve.resolvconf.iface_prefix = lo.inet.ipsec.
	Prefix used for interface names sent to resolvconf(8).

	Prefix used for interface names sent to **resolvconf**(8). The nameserver
	address is appended to this prefix to make it unique.  The result has to be
	a valid interface name according to the rules defined by resolvconf.  Also,
	it should have a high priority according to the order defined in
	**interface-order**(5).
