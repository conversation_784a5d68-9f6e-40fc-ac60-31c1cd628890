tnccs-20 {

    # Whether to load the plugin. Can also be an integer to increase the
    # priority of this plugin.
    load = yes

    # Maximum size of a PB-TNC batch (upper limit via PT-EAP = 65529).
    # max_batch_size = 65522

    # Maximum size of a PA-TNC message (upper limit via PT-EAP = 65497).
    # max_message_size = 65490

    # Enable PB-TNC mutual protocol.
    # mutual = no

    tests {

        # Send an unsupported PB-TNC message type with the NOSKIP flag set.
        # pb_tnc_noskip = no

        # Send a PB-TNC batch with a modified PB-TNC version.
        # pb_tnc_version = 2

    }

}

