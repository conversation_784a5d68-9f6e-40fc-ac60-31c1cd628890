gmalg {

    # Whether to load the plugin. Can also be an integer to increase the
    # priority of this plugin.
    load = yes

    # Whether to enable the GM/T algorithm interior implementation.
    # Set to yes when using kernel AF_ALG interface for GM algorithms.
    interior = yes

    # Enable debug output for gmalg plugin
    # debug = no

    # Socket timeout for AF_ALG operations (in seconds)
    # socket_timeout = 30

    # Enable performance statistics
    # enable_perf_stats = no

    # Hash buffer size for SM3 operations
    # hash_buffer_size = 4096

    # Enable strict key validation
    # strict_key_check = yes

}
