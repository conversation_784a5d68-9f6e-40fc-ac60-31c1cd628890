#!/bin/bash

# 测试编译gmalg插件的脚本
# 用于检查语法错误和编译问题

echo "开始测试编译gmalg插件..."

# 进入strongswan目录
cd /home/<USER>/strongswan-5.9.30

# 清理之前的编译结果
echo "清理之前的编译结果..."
make clean > /dev/null 2>&1

# 只编译gmalg插件
echo "编译gmalg插件..."
cd src/libstrongswan/plugins/gmalg

# 使用gcc直接编译测试语法
echo "测试语法检查..."
gcc -DHAVE_CONFIG_H -I. -I../../../.. -I../../../../src/libstrongswan -I../../../../src/libstrongswan/plugins/gmalg -DGMALG_AF_ALG_ENABLED -rdynamic -g -O2 -Wall -Wno-format -Wno-format-security -Wno-pointer-sign -include /home/<USER>/strongswan-5.9.30/config.h -c gmalg_af_alg.c -o gmalg_af_alg.o

if [ $? -eq 0 ]; then
    echo "gmalg_af_alg.c 编译成功！"
else
    echo "gmalg_af_alg.c 编译失败！"
    exit 1
fi

# 测试编译其他文件
echo "测试编译gmalg_plugin.c..."
gcc -DHAVE_CONFIG_H -I. -I../../../.. -I../../../../src/libstrongswan -I../../../../src/libstrongswan/plugins/gmalg -DGMALG_AF_ALG_ENABLED -rdynamic -g -O2 -Wall -Wno-format -Wno-format-security -Wno-pointer-sign -include /home/<USER>/strongswan-5.9.30/config.h -c gmalg_plugin.c -o gmalg_plugin.o

if [ $? -eq 0 ]; then
    echo "gmalg_plugin.c 编译成功！"
else
    echo "gmalg_plugin.c 编译失败！"
    exit 1
fi

echo "所有文件编译测试完成！"
