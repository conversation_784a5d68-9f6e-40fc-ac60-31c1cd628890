/* SPDX-License-Identifier: GPL-2.0 */

/*
 * SM4分组密码算法的通用值定义
 * 增强版本，同时支持原厂家SDK和strongSwan需求
 * Copyright (C) 2018 ARM Limited or its affiliates.
 * Enhanced for strongSwan support
 */

#ifndef _CRYPTO_SM4_H
#define _CRYPTO_SM4_H

#include <linux/types.h>
#include <linux/crypto.h>

/* SM4算法常量定义 */
#define SM4_KEY_SIZE	16    /* SM4密钥长度：128位 */
#define SM4_BLOCK_SIZE	16    /* SM4块大小：128位 */
#define SM4_RKEY_WORDS	32    /* SM4轮密钥字数：32个32位字 */

/* SM4密钥扩展上下文结构体 */
struct crypto_sm4_ctx {
	u32 rkey_enc[SM4_RKEY_WORDS];  /* 加密轮密钥 */
	u32 rkey_dec[SM4_RKEY_WORDS];  /* 解密轮密钥 */
};

/* 原始函数声明（保持与Linux内核兼容性） */

/**
 * crypto_sm4_set_key - 设置SM4密钥
 * @tfm: 加密变换上下文
 * @in_key: 输入密钥
 * @key_len: 密钥长度
 * 
 * 返回: 成功时返回0，密钥长度无效时返回-EINVAL
 */
extern int crypto_sm4_set_key(struct crypto_tfm *tfm, const u8 *in_key,
		              unsigned int key_len);

/**
 * crypto_sm4_expand_key - 扩展SM4密钥
 * @ctx: SM4上下文
 * @in_key: 输入密钥
 * @key_len: 密钥长度
 * 
 * 返回: 成功时返回0，密钥长度无效时返回-EINVAL
 */
extern int crypto_sm4_expand_key(struct crypto_sm4_ctx *ctx, const u8 *in_key,
			         unsigned int key_len);

/**
 * crypto_sm4_encrypt - SM4加密单个块
 * @tfm: 加密变换上下文
 * @out: 输出缓冲区
 * @in: 输入缓冲区
 */
extern void crypto_sm4_encrypt(struct crypto_tfm *tfm, u8 *out, const u8 *in);

/**
 * crypto_sm4_decrypt - SM4解密单个块
 * @tfm: 加密变换上下文
 * @out: 输出缓冲区
 * @in: 输入缓冲区
 */
extern void crypto_sm4_decrypt(struct crypto_tfm *tfm, u8 *out, const u8 *in);

/* strongSwan兼容函数声明 */

/**
 * sm4_ecb_encrypt - SM4 ECB模式加密
 * @key: 128位密钥
 * @input: 输入数据（必须是16字节的倍数）
 * @output: 输出缓冲区
 * @length: 数据长度（必须是16字节的倍数）
 * 
 * 使用SM4算法的ECB（电子密码本）模式进行加密
 * ECB模式对每个块独立加密，适用于加密少量数据
 * 
 * 返回: 成功时返回0，参数无效时返回负错误码
 */
extern int sm4_ecb_encrypt(const u8 *key, const u8 *input, u8 *output, size_t length);

/**
 * sm4_ecb_decrypt - SM4 ECB模式解密
 * @key: 128位密钥
 * @input: 输入密文（必须是16字节的倍数）
 * @output: 输出缓冲区
 * @length: 数据长度（必须是16字节的倍数）
 * 
 * 使用SM4算法的ECB模式进行解密
 * 
 * 返回: 成功时返回0，参数无效时返回负错误码
 */
extern int sm4_ecb_decrypt(const u8 *key, const u8 *input, u8 *output, size_t length);

/**
 * sm4_cbc_encrypt - SM4 CBC模式加密
 * @key: 128位密钥
 * @iv: 16字节初始向量
 * @input: 输入数据（必须是16字节的倍数）
 * @output: 输出缓冲区
 * @length: 数据长度（必须是16字节的倍数）
 * 
 * 使用SM4算法的CBC（密码块链接）模式进行加密
 * CBC模式通过将前一个密文块与当前明文块异或来增强安全性
 * 
 * 返回: 成功时返回0，参数无效时返回负错误码
 */
extern int sm4_cbc_encrypt(const u8 *key, const u8 *iv, const u8 *input, 
		           u8 *output, size_t length);

/**
 * sm4_cbc_decrypt - SM4 CBC模式解密
 * @key: 128位密钥
 * @iv: 16字节初始向量
 * @input: 输入密文（必须是16字节的倍数）
 * @output: 输出缓冲区
 * @length: 数据长度（必须是16字节的倍数）
 * 
 * 使用SM4算法的CBC模式进行解密
 * 
 * 返回: 成功时返回0，参数无效时返回负错误码
 */
extern int sm4_cbc_decrypt(const u8 *key, const u8 *iv, const u8 *input, 
		           u8 *output, size_t length);

/**
 * sm4_cfb_encrypt - SM4 CFB模式加密
 * @key: 128位密钥
 * @iv: 16字节初始向量
 * @input: 输入数据（长度可以不是块大小的倍数）
 * @output: 输出缓冲区
 * @length: 数据长度
 * 
 * 使用SM4算法的CFB（密码反馈）模式进行加密
 * CFB模式将分组密码转换为流密码，可以处理任意长度的数据
 * 
 * 返回: 成功时返回0，参数无效时返回负错误码
 */
extern int sm4_cfb_encrypt(const u8 *key, const u8 *iv, const u8 *input, 
		           u8 *output, size_t length);

/**
 * sm4_cfb_decrypt - SM4 CFB模式解密
 * @key: 128位密钥
 * @iv: 16字节初始向量
 * @input: 输入密文（长度可以不是块大小的倍数）
 * @output: 输出缓冲区
 * @length: 数据长度
 * 
 * 使用SM4算法的CFB模式进行解密
 * 
 * 返回: 成功时返回0，参数无效时返回负错误码
 */
extern int sm4_cfb_decrypt(const u8 *key, const u8 *iv, const u8 *input, 
		           u8 *output, size_t length);

/**
 * sm4_ofb_crypt - SM4 OFB模式加密/解密
 * @key: 128位密钥
 * @iv: 16字节初始向量
 * @input: 输入数据（长度可以不是块大小的倍数）
 * @output: 输出缓冲区
 * @length: 数据长度
 * 
 * 使用SM4算法的OFB（输出反馈）模式进行加密或解密
 * OFB模式的加密和解密操作相同，将分组密码转换为流密码
 * 
 * 返回: 成功时返回0，参数无效时返回负错误码
 */
extern int sm4_ofb_crypt(const u8 *key, const u8 *iv, const u8 *input, 
		         u8 *output, size_t length);

/**
 * sm4_ctr_crypt - SM4 CTR模式加密/解密
 * @key: 128位密钥
 * @iv: 16字节计数器初始值
 * @input: 输入数据（长度可以不是块大小的倍数）
 * @output: 输出缓冲区
 * @length: 数据长度
 * 
 * 使用SM4算法的CTR（计数器）模式进行加密或解密
 * CTR模式通过加密递增的计数器值生成密钥流，加密和解密操作相同
 * 
 * 返回: 成功时返回0，参数无效时返回负错误码
 */
extern int sm4_ctr_crypt(const u8 *key, const u8 *iv, const u8 *input, 
		         u8 *output, size_t length);

#endif /* _CRYPTO_SM4_H */