charon.plugins.eap-dynamic.preferred =
	The preferred EAP method(s) to be used.

	The preferred EAP method(s) to be used.  If it is not given the first
	registered method will be used initially.  If a comma separated list is
	given the methods are tried in the given order before trying the rest of
	the registered methods.

charon.plugins.eap-dynamic.prefer_user = no
	Prefer peer's proposed EAP methods.

	If enabled the EAP methods proposed in an EAP-Nak message sent by the peer
	are preferred over the methods registered locally.
