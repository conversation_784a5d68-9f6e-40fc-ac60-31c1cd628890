charon.plugins.systime-fix.interval = 0
	Interval in seconds to check system time for validity. 0 disables the check.

charon.plugins.systime-fix.reauth = no
	Whether to use reauth or delete if an invalid cert lifetime is detected.

charon.plugins.systime-fix.threshold =
	Threshold date where system time is considered valid. Disabled if not
	specified.

charon.plugins.systime-fix.threshold_format = %Y
	**strptime**(3) format used to parse threshold option.

charon.plugins.systime-fix.timeout = 0s
	How long to wait for a valid system time if an interval is configured. 0 to
	recheck indefinitely.
