charon.plugins.tnc-pdp.pt_tls.enable = yes
	Enable PT-TLS protocol on the strongSwan PDP.

charon.plugins.tnc-pdp.pt_tls.port = 271
	PT-TLS server port the strongSwan PDP is listening on.

charon.plugins.tnc-pdp.radius.enable = yes
	Enable RADIUS protocol on the strongSwan PDP.

charon.plugins.tnc-pdp.radius.method = ttls
	EAP tunnel method to be used.

charon.plugins.tnc-pdp.radius.port = 1812
	RADIUS server port the strongSwan PDP is listening on.

charon.plugins.tnc-pdp.radius.secret =
	Shared RADIUS secret between strongSwan PDP and NAS. If set, make sure to
	adjust the permissions of the config file accordingly.

charon.plugins.tnc-pdp.server =
	Name of the strongSwan PDP as contained in the AAA certificate.

charon.plugins.tnc-pdp.timeout =
	Timeout in seconds before closing incomplete connections.
