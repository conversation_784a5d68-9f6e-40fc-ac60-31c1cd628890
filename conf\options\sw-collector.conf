# Options for the sw-collector tool.
sw-collector {

    # URI to software collector database containing event timestamps, software
    # creation and deletion events and collected software identifiers.
    # database =

    # Path pointing to file created when the Linux OS was installed.
    # first_file = /var/log/bootstrap.log

    # Time in UTC when the Linux OS was installed.
    # first_time = 0000-00-00T00:00:00Z

    # Path pointing to apt history.log file.
    # history =

    # Plugins to load in sw-collector tool.
    # load =

    rest_api {

        # Timeout of REST API HTTP POST transaction.
        # timeout = 120

        # HTTP URI of the central collector's REST API.
        # uri =

    }

}

