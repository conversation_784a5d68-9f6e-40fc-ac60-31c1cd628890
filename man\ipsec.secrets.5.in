.TH IPSEC.SECRETS 5 "2011-12-14" "@PACKAGE_VERSION@" "strongSwan"
.SH NAME
ipsec.secrets \- secrets for IKE/IPsec authentication
.SH DESCRIPTION
The file \fIipsec.secrets\fP holds a table of secrets.
These secrets are used by the strongSwan Internet Key Exchange (IKE) daemons
pluto (IKEv1) and charon (IKEv2) to authenticate other hosts.
.LP
It is vital that these secrets be protected.  The file should be owned
by the super-user,
and its permissions should be set to block all access by others.
.LP
The file is a sequence of entries and include directives.
Here is an example.
.LP
.RS
.nf
# /etc/ipsec.secrets - strongSwan IPsec secrets file
*********** %any : PSK "v+NkxY9LLZvwj4qCC2o/gGrWDF2d21jL"

: RSA moonKey.pem

<EMAIL> : EAP "x3.dEhgN"

carol : XAUTH "4iChxLT3"

dave  : XAUTH "ryftzG4A"

# get secrets from other files
include ipsec.*.secrets
.fi
.RE
.LP
Each entry in the file is a list of optional ID selectors, followed by a secret.
The two parts are separated by a colon (\fB:\fP) that is surrounded
by whitespace. If no ID selectors are specified the line must start with a
colon.
.LP
A selector is an IP address, a Fully Qualified Domain Name, user@FQDN,
\fB%any\fP or \fB%any6\fP (other kinds may come).
.LP
Matching IDs with selectors is fairly straightforward: they have to be
equal.  In the case of a ``Road Warrior'' connection, if an equal
match is not found for the Peer's ID, and it is in the form of an IP
address, a selector of \fB%any\fP will match the peer's IP address if IPV4
and \fB%any6\fP will match a the peer's IP address if IPV6.
Currently, the obsolete notation \fB0.0.0.0\fP may be used in place of
\fB%any\fP.
.LP
In IKEv1 an additional complexity
arises in the case of authentication by preshared secret: the
responder will need to look up the secret before the Peer's ID payload has
been decoded, so the ID used will be the IP address.
.LP
To authenticate a connection between two hosts, the entry that most
specifically matches the host and peer IDs is used.  An entry with no
selectors will match any host and peer.  More specifically, an entry with one
selector will match a host and peer if the selector matches the host's ID (the
peer isn't considered).  Still more specifically, an entry with multiple
selectors will match a host and peer if the host ID and peer ID each match one
of the selectors.  If the key is for an asymmetric authentication technique
(i.e. a public key system such as RSA), an entry with multiple selectors will
match a host and peer even if only the host ID matches a selector (it is
presumed that the selectors are all identities of the host).
It is acceptable for two entries to be the best match as
long as they agree about the secret or private key.
.LP
Authentication by preshared secret requires that both systems find the
identical secret (the secret is not actually transmitted by the IKE
protocol).  If both the host and peer appear in the selector list, the
same entry will be suitable for both systems so verbatim copying
between systems can be used.  This naturally extends to larger groups
sharing the same secret.  Thus multiple-selector entries are best for PSK
authentication.
.LP
Authentication by public key systems such as RSA requires that each host
have its own private key.  A host could reasonably use a different private keys
for different interfaces and for different peers.  But it would not
be normal to share entries between systems.  Thus thus no-selector and
one-selector forms of entry often make sense for public key authentication.
.LP
The key part of an entry must start with a token indicating the kind of
key.  The following types of secrets are currently supported:
.TP
.B PSK
defines a pre-shared key
.TP
.B RSA
defines an RSA private key
.TP
.B ECDSA
defines an ECDSA private key
.TP
.B P12
defines a PKCS#12 container
.TP
.B EAP
defines EAP credentials
.TP
.B NTLM
defines NTLM credentials
.TP
.B XAUTH
defines XAUTH credentials
.TP
.B PIN
defines a smartcard PIN
.LP
Details on each type of secret are given below.
.LP
Whitespace at the end of a line is ignored. At the start of a line or
after whitespace, \fB#\fP and the following text up to the end of the
line is treated as a comment.
.LP
An include directive causes the contents of the named file to be processed
before continuing with the current file.  The filename is subject to
``globbing'' as in \fIsh\fP(1), so every file with a matching name
is processed.  Includes may be nested to a modest
depth (10, currently).  If the filename doesn't start with a \fB/\fP, the
directory containing the current file is prepended to the name.  The
include directive is a line that starts with the word \fBinclude\fP,
followed by whitespace, followed by the filename (which must not contain
whitespace).
.SS TYPES OF SECRETS
.TP
.B [ <selectors> ] : PSK <secret>
A preshared \fIsecret\fP is most conveniently represented as a sequence of
characters, which is delimited by double-quote characters (\fB"\fP).
The sequence cannot contain newline or double-quote characters.
.br
Alternatively, preshared secrets can be represented as hexadecimal or Base64
encoded binary values. A character sequence beginning with
.B 0x
is interpreted as sequence of hexadecimal digits.
Similarly, a character sequence beginning with
.B 0s
is interpreted as Base64 encoded binary data.
.TP
.B : RSA <private key file> [ <passphrase> | %prompt ]
.TQ
.B : ECDSA <private key file> [ <passphrase> | %prompt ]
For the private key file both absolute paths or paths relative to
\fI/etc/ipsec.d/private\fP are accepted. If the private key file is
encrypted, the \fIpassphrase\fP must be defined. Instead of a passphrase
.B %prompt
can be used which then causes the daemon to ask the user for the password
whenever it is required to decrypt the key.
.TP
.B : P12 <PKCS#12 file> [ <passphrase> | %prompt ]
For the PKCS#12 file both absolute paths or paths relative to
\fI/etc/ipsec.d/private\fP are accepted. If the container is
encrypted, the \fIpassphrase\fP must be defined. Instead of a passphrase
.B %prompt
can be used which then causes the daemon to ask the user for the password
whenever it is required to decrypt the container. Private keys, client and CA
certificates are extracted from the container. To use such a client certificate
in a connection set leftid to one of the subjects of the certificate.
.TP
.B <user id> : EAP <secret>
The format of \fIsecret\fP is the same as that of \fBPSK\fP secrets.
.br
\fBEAP\fP secrets are IKEv2 only.
.TP
.B <user id> : NTLM <secret>
The format of \fIsecret\fP is the same as that of \fBPSK\fP secrets, but the
secret is stored as NTLM hash, which is MD4(UTF-16LE(secret)), instead of as
cleartext.
.br
\fBNTLM\fP secrets can only be used with the \fBeap-mschapv2\fP plugin.
.TP
.B [ <servername> ] <username> : XAUTH <password>
The format of \fIpassword\fP is the same as that of \fBPSK\fP secrets.
\fBXAUTH\fP secrets are IKEv1 only.
.TP
.B : PIN %smartcard[<slot nr>[@<module>]]:<keyid> <pin code> | %prompt
The smartcard selector always requires a keyid to uniquely select the correct
key. The slot number defines the slot on the token, the module name refers to
the module name defined in strongswan.conf(5).
Instead of specifying the pin code statically,
.B %prompt
can be specified, which causes the daemon to ask the user for the pin code.
.LP

.SH FILES
/etc/ipsec.secrets
.SH SEE ALSO
ipsec.conf(5), strongswan.conf(5), ipsec(8)
.br
.SH HISTORY
Originally written for the FreeS/WAN project by D. Hugh Redelmeier.
Updated and extended for the strongSwan project <http://www.strongswan.org> by
Tobias Brunner and Andreas Steffen.
.SH BUGS
If an ID is \fB0.0.0.0\fP, it will match \fB%any\fP;
if it is \fB0::0\fP, it will match \fB%any6\fP.
