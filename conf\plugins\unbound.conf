unbound {

    # File to read trusted keys for DLV (DNSSEC Lookaside Validation) from.
    # dlv_anchors =

    # Whether to load the plugin. Can also be an integer to increase the
    # priority of this plugin.
    load = yes

    # File to read DNS resolver configuration from.
    # resolv_conf = /etc/resolv.conf

    # File to read DNSSEC trust anchors from (usually root zone KSK).
    # trust_anchors = /etc/ipsec.d/dnssec.keys

}

