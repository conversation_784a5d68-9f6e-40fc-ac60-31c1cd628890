charon.plugins.ha.autobalance = 0
	Interval in seconds to automatically balance handled segments between nodes.
	Set to 0 to disable.

charon.plugin.ha.buflen = 2048
	Buffer size for received HA messages.

	Buffer size for received HA messages. For IKEv1 the public DH factors are
	also transmitted so depending on the DH group the HA messages can get quite
	big (the default should be fine up to _modp4096_).

charon.plugins.ha.fifo_interface = yes

charon.plugins.ha.heartbeat_delay = 1000

charon.plugins.ha.heartbeat_timeout = 2100

charon.plugins.ha.local =

charon.plugins.ha.monitor = yes

charon.plugins.ha.pools =

charon.plugins.ha.remote =

charon.plugins.ha.resync = yes

charon.plugins.ha.secret =

charon.plugins.ha.segment_count = 1
